---
description: 项目结构和文件组织规范
---

# MineAdmin 项目结构和文件组织规范

## 项目根目录结构

```
mine-admin-baccarat/
├── .cursor/                    # Cursor 配置和规则
│   └── rules/                 # Cursor 规则文件
├── .devcontainer/             # 开发容器配置
├── .github/                   # GitHub 工作流和模板
├── app/                       # PHP 后端应用代码
├── bin/                       # 可执行文件
├── config/                    # Hyperf 配置文件
├── databases/                 # 数据库迁移和种子文件
├── plugin/                    # 插件目录
├── runtime/                   # 运行时缓存和日志
├── storage/                   # 文件存储目录
├── tests/                     # 后端测试文件
├── vendor/                    # Composer 依赖包
├── web/                       # 前端项目目录
├── composer.json              # PHP 依赖配置
├── docker-compose.yml         # Docker 编排配置
├── Dockerfile                 # Docker 镜像构建
├── phpunit.xml.dist          # PHPUnit 测试配置
└── README.md                  # 项目说明文档
```

## 后端目录结构 (app/)

```
app/
├── Command/                   # 控制台命令
│   ├── ApplicationInstallCommand.php
│   └── ...
├── Exception/                 # 自定义异常类
│   ├── Handler/              # 异常处理器
│   └── ...
├── Http/                      # HTTP 层
│   ├── Admin/                # 后台管理 API
│   │   ├── Controller/       # 控制器
│   │   ├── Middleware/       # 中间件
│   │   └── Request/          # 请求验证类
│   ├── Api/                  # 前台 API
│   └── Common/               # 公共 HTTP 组件
├── Model/                     # 数据模型
│   ├── Casts/                # 模型类型转换器
│   ├── Enums/                # 枚举类
│   ├── Permission/           # 权限相关模型
│   └── ...
├── Repository/                # 数据仓库层
├── Schema/                    # 数据库模式定义
├── Service/                   # 业务逻辑服务层
└── ...
```

## 前端目录结构 (web/src/)

```
web/src/
├── assets/                    # 静态资源
│   ├── images/               # 图片资源
│   ├── icons/                # 图标文件
│   └── styles/               # 样式文件
├── components/                # 公共组件
│   ├── base/                 # 基础组件
│   ├── business/             # 业务组件
│   └── layout/               # 布局组件
├── directives/                # Vue 自定义指令
├── hooks/                     # Vue 组合式函数
├── layouts/                   # 页面布局组件
│   ├── AdminLayout.vue       # 后台布局
│   ├── UserLayout.vue        # 用户布局
│   └── BlankLayout.vue       # 空白布局
├── locales/                   # 国际化文件
│   ├── zh-CN.ts              # 中文
│   ├── en-US.ts              # 英文
│   └── index.ts              # 国际化配置
├── modules/                   # 功能模块
│   ├── base/                 # 基础模块
│   │   ├── api/              # API 接口
│   │   ├── components/       # 模块组件
│   │   ├── store/            # 状态管理
│   │   ├── types/            # 类型定义
│   │   └── views/            # 页面组件
│   ├── user/                 # 用户模块
│   └── ...
├── plugins/                   # Vue 插件
├── provider/                  # 服务提供者
├── router/                    # 路由配置
│   ├── modules/              # 路由模块
│   ├── guards/               # 路由守卫
│   └── index.ts              # 路由入口
├── store/                     # Pinia 状态管理
│   ├── modules/              # 状态模块
│   └── index.ts              # Store 入口
├── utils/                     # 工具函数
│   ├── request.ts            # HTTP 请求封装
│   ├── auth.ts               # 认证工具
│   ├── storage.ts            # 存储工具
│   └── ...
├── App.vue                    # 根组件
├── main.ts                    # 应用入口
└── bootstrap.ts               # 应用引导
```

## 文件命名规范

### 后端文件命名

1. **PHP 类文件**: PascalCase，如 `UserController.php`
2. **配置文件**: snake_case，如 `database.php`
3. **迁移文件**: timestamp + snake_case，如 `2023_01_01_000000_create_users_table.php`
4. **测试文件**: PascalCase + Test，如 `UserControllerTest.php`

### 前端文件命名

1. **Vue 组件**: PascalCase，如 `UserList.vue`
2. **TypeScript 文件**: camelCase，如 `userApi.ts`
3. **样式文件**: kebab-case，如 `user-list.scss`
4. **工具函数**: camelCase，如 `formatDate.ts`

## 模块组织规范

### 后端模块组织

每个功能模块应包含以下结构：

```
Module/
├── Controller/                # 控制器
├── Service/                   # 服务层
├── Repository/                # 仓库层
├── Model/                     # 模型
├── Request/                   # 请求验证
├── Resource/                  # 资源转换
├── Event/                     # 事件
├── Listener/                  # 事件监听器
├── Job/                       # 队列任务
├── Middleware/                # 中间件
└── Exception/                 # 异常类
```

### 前端模块组织

每个功能模块应包含以下结构：

```
module/
├── api/                       # API 接口定义
│   ├── index.ts              # 导出所有 API
│   └── userApi.ts            # 具体 API
├── components/                # 模块组件
│   ├── UserForm.vue          # 用户表单
│   └── UserCard.vue          # 用户卡片
├── store/                     # 模块状态管理
│   ├── index.ts              # 导出所有 store
│   └── userStore.ts          # 用户状态
├── types/                     # 类型定义
│   ├── index.ts              # 导出所有类型
│   └── user.ts               # 用户相关类型
├── views/                     # 页面组件
│   ├── UserList.vue          # 用户列表页
│   ├── UserDetail.vue        # 用户详情页
│   └── UserForm.vue          # 用户表单页
└── index.ts                   # 模块导出
```

## 导入导出规范

### 后端导入规范

```php
<?php

declare(strict_types=1);

namespace App\Http\Admin\Controller;

use App\Model\User;
use App\Service\UserService;
use App\Http\Admin\Request\UserRequest;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\PostMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Mine\MineController;

#[Controller(prefix: '/admin/user')]
class UserController extends MineController
{
    // 控制器实现
}
```

### 前端导入规范

```typescript
// 第三方库导入
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";

// 项目内部导入
import { userApi } from "@/modules/user/api";
import { useUserStore } from "@/modules/user/store";
import type { UserItem, UserForm } from "@/modules/user/types";

// 组件导入
import UserForm from "./components/UserForm.vue";
import UserCard from "./components/UserCard.vue";
```

### 导出规范

```typescript
// 具名导出
export { userApi } from "./userApi";
export { useUserStore } from "./userStore";
export type { UserItem, UserForm } from "./types";

// 默认导出
export default {
  userApi,
  useUserStore,
};

// 重新导出
export * from "./user";
export * from "./role";
```

## 配置文件组织

### 后端配置 (config/)

```
config/
├── autoload/                  # 自动加载配置
│   ├── cache.php             # 缓存配置
│   ├── databases.php         # 数据库配置
│   ├── redis.php             # Redis 配置
│   └── ...
├── config.php                 # 主配置文件
├── container.php              # 容器配置
└── routes.php                 # 路由配置
```

### 前端配置

```
web/
├── vite.config.ts             # Vite 构建配置
├── tsconfig.json              # TypeScript 配置
├── uno.config.ts              # UnoCSS 配置
├── eslint.config.js           # ESLint 配置
├── stylelint.config.js        # Stylelint 配置
├── package.json               # 项目依赖配置
└── .env.example               # 环境变量示例
```

## 测试文件组织

### 后端测试 (tests/)

```
tests/
├── Feature/                   # 功能测试
│   ├── Admin/                # 后台功能测试
│   ├── Api/                  # API 功能测试
│   └── Command/              # 命令测试
├── Unit/                      # 单元测试
│   ├── Service/              # 服务层测试
│   ├── Repository/           # 仓库层测试
│   └── Model/                # 模型测试
├── bootstrap.php              # 测试引导文件
└── HttpTestCase.php           # HTTP 测试基类
```

### 前端测试 (web/tests/)

```
web/tests/
├── unit/                      # 单元测试
│   ├── components/           # 组件测试
│   ├── utils/                # 工具函数测试
│   └── store/                # 状态管理测试
├── e2e/                       # 端到端测试
│   ├── specs/                # 测试规范
│   └── fixtures/             # 测试数据
└── setup.ts                   # 测试设置
```

## 静态资源组织

### 图片资源

```
web/src/assets/images/
├── logo/                      # 品牌 Logo
├── icons/                     # 图标文件
├── avatars/                   # 头像图片
├── backgrounds/               # 背景图片
└── placeholders/              # 占位图片
```

### 样式文件

```
web/src/assets/styles/
├── variables/                 # CSS 变量
│   ├── colors.scss           # 颜色变量
│   ├── sizes.scss            # 尺寸变量
│   └── fonts.scss            # 字体变量
├── mixins/                    # SCSS 混入
├── base/                      # 基础样式
│   ├── reset.scss            # 样式重置
│   ├── typography.scss       # 字体排版
│   └── layout.scss           # 布局样式
├── components/                # 组件样式
└── themes/                    # 主题样式
```

## 文档组织

```
docs/                          # 项目文档（可选）
├── api/                       # API 文档
├── development/               # 开发文档
├── deployment/                # 部署文档
└── user/                      # 用户手册
```

## 环境文件

```
.env.example                   # 环境变量示例
.env.local                     # 本地环境变量
.env.testing                   # 测试环境变量
.env.production                # 生产环境变量
```

## Git 忽略规则

确保 `.gitignore` 包含以下内容：

```gitignore
# 依赖目录
/vendor/
/node_modules/
/web/node_modules/

# 运行时文件
/runtime/
/storage/

# 环境配置
.env
.env.local

# 构建产物
/web/dist/
/web/build/

# IDE 配置
.vscode/
.idea/

# 系统文件
.DS_Store
Thumbs.db
```

## 最佳实践

1. **模块化设计**: 按功能模块组织代码，保持高内聚低耦合
2. **命名一致性**: 在整个项目中保持一致的命名规范
3. **文件大小**: 单个文件不超过 500 行，超出时考虑拆分
4. **导入顺序**: 先导入第三方库，再导入项目内部模块
5. **注释文档**: 为复杂逻辑和公共函数添加注释
6. **版本控制**: 合理使用 Git 分支和提交信息规范
7. **代码审查**: 重要功能变更需要代码审查
8. **测试覆盖**: 关键业务逻辑需要编写测试用例
