---
alwaysApply: true
description: MineAdmin 项目整体架构规则和开发规范
---

# MineAdmin 项目架构规则

## 项目概述

MineAdmin 是基于 Hyperf + Vue3 的前后端分离后台管理系统，采用模块化架构设计。

## 技术栈

- **后端**: PHP 8.1+, Hyperf 3.1, Swoole 5.0+, MySQL/PostgreSQL/SQL Server, Redis
- **前端**: Vue 3, Vite, Pinia, TypeScript, Arco Design, Element Plus
- **开发环境**: Docker, Docker Compose

## 项目结构

### 根目录结构

```
├── app/                    # 后端应用代码
├── web/                    # 前端应用代码
├── config/                 # Hyperf 配置文件
├── plugin/                 # 插件目录
├── storage/                # 存储目录
├── runtime/                # 运行时文件
├── tests/                  # 测试文件
├── bin/                    # 可执行文件
├── databases/              # 数据库迁移和种子文件
├── vendor/                 # Composer 依赖
├── composer.json           # PHP 依赖配置
├── docker-compose.yml      # Docker 容器编排
└── Dockerfile              # Docker 镜像构建
```

### 后端架构 (app/)

```
app/
├── Command/               # 控制台命令
├── Exception/             # 异常处理
├── Http/                  # HTTP 层
│   ├── Admin/            # 后台管理 API
│   ├── Api/              # 前台 API
│   └── Common/           # 公共 HTTP 组件
├── Model/                # 数据模型
│   ├── Casts/           # 模型类型转换
│   ├── Enums/           # 枚举类
│   └── Permission/      # 权限相关模型
├── Repository/           # 数据仓库层
├── Schema/               # 数据库模式
└── Service/              # 业务逻辑层
```

### 前端架构 (web/src/)

```
web/src/
├── assets/               # 静态资源
├── components/           # 公共组件
├── directives/           # Vue 指令
├── hooks/                # Vue 组合式函数
├── layouts/              # 布局组件
├── locales/              # 国际化文件
├── modules/              # 功能模块
│   └── base/            # 基础模块
├── plugins/              # Vue 插件
├── provider/             # 服务提供者
├── router/               # 路由配置
├── store/                # Pinia 状态管理
├── utils/                # 工具函数
├── App.vue               # 根组件
├── main.ts               # 应用入口
└── bootstrap.ts          # 应用引导
```

## 开发规范

### 后端开发规范

1. **控制器**: 放置在 `app/Http/Admin/` 或 `app/Http/Api/` 目录
2. **模型**: 放置在 `app/Model/` 目录，继承 Hyperf Model 基类
3. **服务层**: 放置在 `app/Service/` 目录，处理业务逻辑
4. **仓库层**: 放置在 `app/Repository/` 目录，处理数据访问
5. **异常**: 自定义异常放置在 `app/Exception/` 目录
6. **命令**: 控制台命令放置在 `app/Command/` 目录

### 前端开发规范

1. **组件**: 公共组件放在 `components/`，模块组件放在对应模块目录
2. **页面**: 页面组件放在 `modules/{模块}/views/` 目录
3. **API**: API 请求放在 `modules/{模块}/api/` 目录
4. **状态管理**: Pinia store 放在 `store/` 目录
5. **路由**: 路由配置放在 `router/` 目录
6. **工具函数**: 放在 `utils/` 目录

### 命名规范

- **PHP 类**: 使用 PascalCase（如：UserController）
- **PHP 方法/变量**: 使用 camelCase（如：getUserList）
- **Vue 组件**: 使用 PascalCase（如：UserList.vue）
- **文件名**: 使用 kebab-case 或 PascalCase
- **常量**: 使用 SCREAMING_SNAKE_CASE

## 核心依赖

### 后端核心包

- `hyperf/framework`: Hyperf 核心框架
- `hyperf/http-server`: HTTP 服务器
- `hyperf/database`: 数据库 ORM
- `hyperf/redis`: Redis 客户端
- `hyperf/validation`: 数据验证
- `mineadmin/core`: MineAdmin 核心包
- `mineadmin/auth-jwt`: JWT 认证

### 前端核心包

- `vue`: Vue 3 框架
- `vue-router`: 路由管理
- `pinia`: 状态管理
- `element-plus`: UI 组件库
- `@mineadmin/pro-table`: 专业表格组件
- `@mineadmin/form`: 表单组件
- `axios`: HTTP 客户端

## 数据库设计规范

1. **表名**: 使用小写 + 下划线，如 `user_login_log`
2. **字段名**: 使用小写 + 下划线，如 `created_at`
3. **主键**: 统一使用 `id`，类型为自增整型
4. **时间戳**: 使用 `created_at` 和 `updated_at`
5. **软删除**: 使用 `deleted_at` 字段

## API 设计规范

1. **RESTful 风格**: 遵循 REST API 设计原则
2. **URL 格式**: `/api/admin/{resource}` 或 `/api/v1/{resource}`
3. **HTTP 方法**: GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
4. **响应格式**: 统一的 JSON 响应格式
5. **状态码**: 正确使用 HTTP 状态码

## 权限控制

1. **认证**: 基于 JWT 的用户认证
2. **授权**: 基于角色的权限控制 (RBAC)
3. **菜单权限**: 动态菜单生成
4. **数据权限**: 按部门或用户级别的数据过滤

## 插件系统

- 插件放置在 `plugin/` 目录
- 支持热插拔的插件架构
- 插件可包含前后端代码

## 开发命令

### 后端开发

```bash
# 启动开发服务器
php bin/hyperf.php start

# 热重载开发
php bin/hyperf.php server:watch

# 运行测试
composer test

# 代码格式化
composer cs-fix

# 静态分析
composer analyse
```

### 前端开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 代码检查
pnpm lint
```

## 部署规范

1. **容器化**: 使用 Docker 进行容器化部署
2. **环境配置**: 通过环境变量管理不同环境配置
3. **数据库迁移**: 使用 Hyperf Migrate 管理数据库版本
4. **静态资源**: 前端资源通过 Nginx 代理或 CDN 分发

## 性能优化

1. **数据库**: 合理使用索引，避免 N+1 查询
2. **缓存**: 充分利用 Redis 缓存热点数据
3. **协程**: 利用 Swoole 协程提升并发性能
4. **前端**: 懒加载、代码分割、静态资源优化

## 安全规范

1. **输入验证**: 严格验证所有用户输入
2. **SQL 注入**: 使用 ORM 防止 SQL 注入
3. **XSS 防护**: 对输出进行适当转义
4. **CSRF 防护**: 实施 CSRF 令牌验证
5. **敏感信息**: 不在代码中硬编码敏感信息
