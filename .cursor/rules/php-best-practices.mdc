---
alwaysApply: true
---
# PHP 软件开发规范

本规范旨在为 PHP 项目提供一套统一的编码标准和最佳实践，以提高代码质量、可读性和可维护性。

## 1. 编码标准

-   **PSR-12**: 所有 PHP 代码必须遵循 [PSR-12 (Extended Coding Style)](https://www.php-fig.org/psr/psr-12/) 规范。这是 PHP 社区广泛接受的标准，统一了代码格式。
-   **自动格式化**: 建议在项目中配置 [PHP-CS-Fixer](https://github.com/FriendsOfPHP/PHP-CS-Fixer) 来自动强制执行代码风格。

## 2. 严格类型

-   **启用严格模式**: 在每个 PHP 文件的开头，都应该使用 `declare(strict_types=1);` 来启用严格类型模式。这可以防止许多由于类型转换引发的隐蔽错误。

## 3. 命名约定

-   **类 (Classes)**: `PascalCase` (大驼峰命名法)。例如: `MyClass`。
-   **常量 (Constants)**: `UPPER_CASE_WITH_UNDERSCORES` (大写下划线命名法)。例如: `MY_CONSTANT`。
-   **方法和函数 (Methods & Functions)**: `camelCase` (小驼峰命名法)。例如: `myFunction`。
-   **变量 (Variables)**: `camelCase` (小驼峰命名法)。例如: `$myVariable`。

## 4. 类型提示 (Type Hinting)

-   **函数/方法参数**: 必须为所有参数添加类型提示。
-   **返回值**: 必须为所有函数和方法添加返回类型提示。
-   **PHP 7.4+**: 鼓励使用类属性的类型提示。

## 5. 面向对象编程 (OOP) 原则

### 5.1 SOLID 原则

-   **单一职责原则 (SRP)**: 每个类只应有一个改变的理由，专注于单一功能。
-   **开闭原则 (OCP)**: 类应该对扩展开放，对修改关闭。使用接口和抽象类来实现。
-   **里氏替换原则 (LSP)**: 子类必须能够替换其基类而不破坏程序功能。
-   **接口隔离原则 (ISP)**: 客户端不应该依赖它不使用的接口方法。
-   **依赖倒置原则 (DIP)**: 高层模块不应依赖低层模块，两者都应依赖抽象。

### 5.2 设计模式

-   **优先使用组合而非继承**: 通过组合来实现代码复用，增加灵活性。
-   **使用工厂模式**: 当对象创建逻辑复杂时，使用工厂模式封装创建逻辑。
-   **策略模式**: 当有多种算法可以完成同一任务时，使用策略模式。
-   **观察者模式**: 用于实现事件驱动的架构。

## 6. 代码简洁性 (Clean Code)

### 6.1 函数和方法

-   **保持函数简短**: 一个函数应该只做一件事，通常不超过 20 行。
-   **有意义的命名**: 函数名应该清楚地表达其功能，避免使用缩写。
-   **减少参数数量**: 函数参数不应超过 3-4 个，考虑使用对象封装多个参数。
-   **避免深层嵌套**: 使用早期返回 (early return) 来减少嵌套层级。

### 6.2 变量和常量

-   **使用有意义的变量名**: 变量名应该自解释，避免使用 `$data`、`$info` 等模糊名称。
-   **避免魔法数字**: 使用命名常量代替硬编码的数字。
-   **最小化变量作用域**: 在最接近使用的地方声明变量。

## 7. 可维护性 (Maintainability)

### 7.1 代码组织

-   **遵循 PSR-4 自动加载标准**: 确保类文件的组织结构清晰。
-   **逻辑分层**: 将业务逻辑、数据访问和表现层分离。
-   **使用命名空间**: 合理使用命名空间来组织代码，避免类名冲突。

### 7.2 依赖管理

-   **使用依赖注入**: 通过构造函数或方法注入依赖，而不是在类内部创建。
-   **使用容器**: 在复杂项目中使用 DI 容器来管理依赖关系。
-   **避免全局状态**: 最小化对全局变量和静态方法的使用。

```php
// 好的示例：依赖注入
class UserController
{
    public function __construct(
        private UserService $userService,
        private ValidationService $validator
    ) {}
}
```

## 8. 可扩展性 (Extensibility)

### 8.1 接口和抽象

-   **面向接口编程**: 依赖接口而非具体实现，便于后续扩展。
-   **使用抽象类**: 为相关类提供公共基础实现。
-   **避免紧耦合**: 类之间应该松耦合，便于独立修改和测试。

### 8.2 配置管理

-   **外部化配置**: 将配置信息从代码中分离出来。
-   **环境特定配置**: 为不同环境（开发、测试、生产）提供不同配置。
-   **使用配置对象**: 将相关配置组织成配置类。

## 11. 文档注释 (PHPDoc)

-   **PHPDoc**: 所有类、方法、函数都应包含符合 [PHPDoc](https://phpdoc.org/) 标准的文档块。
-   **描述清晰**: 注释应该清晰地描述其功能、参数和返回值。
## 12. 现代 PHP 特性

-   **使用新特性**: 鼓励使用 PHP 7 和 PHP 8 引入的现代语言特性，例如：

    -   Null Coalescing Operator (`??`)
    -   Arrow Functions (`fn () => ...`)
    -   Constructor Property Promotion
    -   Named Arguments
    -   Match Expressions
    -   Union Types (`string|int`)
    -   Readonly Properties
    -   Enums (PHP 8.1+)

    -   Constructor Property Promotion
    -   Named Arguments
    -   Match Expressions
# PHP 软件开发规范

本规范旨在为 PHP 项目提供一套统一的编码标准和最佳实践，以提高代码质量、可读性和可维护性。

## 1. 编码标准

-   **PSR-12**: 所有 PHP 代码必须遵循 [PSR-12 (Extended Coding Style)](https://www.php-fig.org/psr/psr-12/) 规范。这是 PHP 社区广泛接受的标准，统一了代码格式。
-   **自动格式化**: 建议在项目中配置 [PHP-CS-Fixer](https://github.com/FriendsOfPHP/PHP-CS-Fixer) 或 [Laravel Pint](https://laravel.com/docs/pint) (在 Laravel 项目中) 来自动强制执行代码风格。

## 2. 严格类型

-   **启用严格模式**: 在每个 PHP 文件的开头，都应该使用 `declare(strict_types=1);` 来启用严格类型模式。这可以防止许多由于类型转换引发的隐蔽错误。

## 3. 命名约定

-   **类 (Classes)**: `PascalCase` (大驼峰命名法)。例如: `MyClass`。
-   **常量 (Constants)**: `UPPER_CASE_WITH_UNDERSCORES` (大写下划线命名法)。例如: `MY_CONSTANT`。
-   **方法和函数 (Methods & Functions)**: `camelCase` (小驼峰命名法)。例如: `myFunction`。
-   **变量 (Variables)**: `camelCase` (小驼峰命名法)。例如: `$myVariable`。

## 4. 类型提示 (Type Hinting)

-   **函数/方法参数**: 必须为所有参数添加类型提示。
-   **返回值**: 必须为所有函数和方法添加返回类型提示。
-   **PHP 7.4+**: 鼓励使用类属性的类型提示。

## 5. 面向对象编程 (OOP) 原则

### 5.1 SOLID 原则

-   **单一职责原则 (SRP)**: 每个类只应有一个改变的理由，专注于单一功能。
-   **开闭原则 (OCP)**: 类应该对扩展开放，对修改关闭。使用接口和抽象类来实现。
-   **里氏替换原则 (LSP)**: 子类必须能够替换其基类而不破坏程序功能。
-   **接口隔离原则 (ISP)**: 客户端不应该依赖它不使用的接口方法。
-   **依赖倒置原则 (DIP)**: 高层模块不应依赖低层模块，两者都应依赖抽象。

### 5.2 设计模式

-   **优先使用组合而非继承**: 通过组合来实现代码复用，增加灵活性。
-   **使用工厂模式**: 当对象创建逻辑复杂时，使用工厂模式封装创建逻辑。
-   **策略模式**: 当有多种算法可以完成同一任务时，使用策略模式。
-   **观察者模式**: 用于实现事件驱动的架构。

## 6. 代码简洁性 (Clean Code)

### 6.1 函数和方法

-   **保持函数简短**: 一个函数应该只做一件事，通常不超过 20 行。
-   **有意义的命名**: 函数名应该清楚地表达其功能，避免使用缩写。
-   **减少参数数量**: 函数参数不应超过 3-4 个，考虑使用对象封装多个参数。
-   **避免深层嵌套**: 使用早期返回 (early return) 来减少嵌套层级。

### 6.2 变量和常量

-   **使用有意义的变量名**: 变量名应该自解释，避免使用 `$data`、`$info` 等模糊名称。
-   **避免魔法数字**: 使用命名常量代替硬编码的数字。
-   **最小化变量作用域**: 在最接近使用的地方声明变量。

## 7. 可维护性 (Maintainability)

### 7.1 代码组织

-   **遵循 PSR-4 自动加载标准**: 确保类文件的组织结构清晰。
-   **逻辑分层**: 将业务逻辑、数据访问和表现层分离。
-   **使用命名空间**: 合理使用命名空间来组织代码，避免类名冲突。

### 7.2 依赖管理

-   **使用依赖注入**: 通过构造函数或方法注入依赖，而不是在类内部创建。
-   **使用容器**: 在复杂项目中使用 DI 容器来管理依赖关系。
-   **避免全局状态**: 最小化对全局变量和静态方法的使用。

```php
// 好的示例：依赖注入
class UserController
{
    public function __construct(
        private UserService $userService,
        private ValidationService $validator
    ) {}
}
```

## 8. 可扩展性 (Extensibility)

### 8.1 接口和抽象

-   **面向接口编程**: 依赖接口而非具体实现，便于后续扩展。
-   **使用抽象类**: 为相关类提供公共基础实现。
-   **避免紧耦合**: 类之间应该松耦合，便于独立修改和测试。

### 8.2 配置管理

-   **外部化配置**: 将配置信息从代码中分离出来。
-   **环境特定配置**: 为不同环境（开发、测试、生产）提供不同配置。
-   **使用配置对象**: 将相关配置组织成配置类。

## 11. 文档注释 (PHPDoc)

-   **PHPDoc**: 所有类、方法、函数都应包含符合 [PHPDoc](https://phpdoc.org/) 标准的文档块。
-   **描述清晰**: 注释应该清晰地描述其功能、参数和返回值。
## 12. 现代 PHP 特性

-   **使用新特性**: 鼓励使用 PHP 7 和 PHP 8 引入的现代语言特性，例如：

    -   Null Coalescing Operator (`??`)
    -   Arrow Functions (`fn () => ...`)
    -   Constructor Property Promotion
    -   Named Arguments
    -   Match Expressions
    -   Union Types (`string|int`)
    -   Readonly Properties
    -   Enums (PHP 8.1+)

    -   Constructor Property Promotion
    -   Named Arguments
    -   Match Expressions
