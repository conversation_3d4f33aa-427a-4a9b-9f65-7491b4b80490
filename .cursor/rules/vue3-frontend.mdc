---
globs: web/**/*.vue,web/**/*.ts,web/**/*.js
description: Vue3 前端开发规范
---

# Vue3 前端开发规范

## 文件引用规范

- 组件目录: [web/src/components/](mdc:web/src/components/)
- 页面组件: [web/src/modules/\*/views/](mdc:web/src/modules/)
- 状态管理: [web/src/store/](mdc:web/src/store/)
- 路由配置: [web/src/router/](mdc:web/src/router/)
- 工具函数: [web/src/utils/](mdc:web/src/utils/)
- 配置文件: [web/vite.config.ts](mdc:web/vite.config.ts)

## Vue 组件开发规范

### 组件文件结构

```vue
<template>
  <div class="user-list">
    <ma-search
      ref="searchRef"
      :columns="searchColumns"
      @search="handleSearch"
    />

    <ma-pro-table
      ref="tableRef"
      :columns="columns"
      :api="userApi.getList"
      row-key="id"
      @selection-change="handleSelectionChange"
    >
      <template #tools>
        <el-button type="primary" @click="handleAdd"> 新增用户 </el-button>
      </template>
    </ma-pro-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { ProTableColumns } from "@mineadmin/pro-table";
import { userApi } from "@/modules/user/api";
import { useUserStore } from "@/modules/user/store";

// 接口定义
interface UserItem {
  id: number;
  username: string;
  email: string;
  created_at: string;
}

// 响应式数据
const tableRef = ref();
const searchRef = ref();
const userStore = useUserStore();

// 表格列定义
const columns: ProTableColumns<UserItem> = [
  { title: "ID", dataIndex: "id", width: 80 },
  { title: "用户名", dataIndex: "username" },
  { title: "邮箱", dataIndex: "email" },
  { title: "创建时间", dataIndex: "created_at" },
  {
    title: "操作",
    dataIndex: "action",
    width: 200,
    fixed: "right",
  },
];

// 搜索列定义
const searchColumns = [
  {
    title: "用户名",
    dataIndex: "username",
    component: "input",
  },
  {
    title: "邮箱",
    dataIndex: "email",
    component: "input",
  },
];

// 事件处理
const handleSearch = (params: any) => {
  tableRef.value?.refresh(params);
};

const handleAdd = () => {
  // 新增用户逻辑
};

const handleSelectionChange = (selection: UserItem[]) => {
  // 选择变更处理
};

// 生命周期
onMounted(() => {
  // 组件挂载后的逻辑
});
</script>

<style scoped lang="scss">
.user-list {
  padding: 16px;

  .search-form {
    margin-bottom: 16px;
  }
}
</style>
```

### 组件命名规范

1. **文件命名**: 使用 PascalCase，如 `UserList.vue`
2. **组件名**: 与文件名保持一致
3. **多词组件**: 组件名必须是多词的，避免与 HTML 元素冲突
4. **基础组件**: 以 `Base`、`App` 或 `Ma` 开头

### TypeScript 使用规范

```typescript
// 接口定义
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

interface UserForm {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
}

// 类型守卫
function isValidUser(user: any): user is UserItem {
  return (
    user && typeof user.id === "number" && typeof user.username === "string"
  );
}

// 枚举使用
enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  PENDING = 2,
}

// 泛型使用
function createApiRequest<T>(url: string): Promise<ApiResponse<T>> {
  return request.get(url);
}
```

### Composition API 规范

```typescript
// 组合式函数
export function useUserManagement() {
  const users = ref<UserItem[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchUsers = async (params = {}) => {
    try {
      loading.value = true;
      const response = await userApi.getList(params);
      users.value = response.data;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const addUser = async (userData: UserForm) => {
    const response = await userApi.create(userData);
    users.value.push(response.data);
    return response.data;
  };

  return {
    users: readonly(users),
    loading: readonly(loading),
    error: readonly(error),
    fetchUsers,
    addUser,
  };
}
```

### Pinia 状态管理

```typescript
// stores/user.ts
import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { UserItem } from "@/types";

export const useUserStore = defineStore("user", () => {
  // 状态
  const users = ref<UserItem[]>([]);
  const currentUser = ref<UserItem | null>(null);
  const loading = ref(false);

  // 计算属性
  const activeUsers = computed(() =>
    users.value.filter((user) => user.status === UserStatus.ACTIVE)
  );

  const userCount = computed(() => users.value.length);

  // 操作
  const setUsers = (newUsers: UserItem[]) => {
    users.value = newUsers;
  };

  const addUser = (user: UserItem) => {
    users.value.push(user);
  };

  const updateUser = (id: number, updates: Partial<UserItem>) => {
    const index = users.value.findIndex((user) => user.id === id);
    if (index !== -1) {
      users.value[index] = { ...users.value[index], ...updates };
    }
  };

  const removeUser = (id: number) => {
    const index = users.value.findIndex((user) => user.id === id);
    if (index !== -1) {
      users.value.splice(index, 1);
    }
  };

  const fetchUsers = async () => {
    loading.value = true;
    try {
      const response = await userApi.getList();
      setUsers(response.data);
    } finally {
      loading.value = false;
    }
  };

  return {
    users,
    currentUser,
    loading,
    activeUsers,
    userCount,
    setUsers,
    addUser,
    updateUser,
    removeUser,
    fetchUsers,
  };
});
```

### API 请求规范

```typescript
// api/user.ts
import { request } from "@/utils/request";
import type { UserItem, UserForm } from "@/types";

export const userApi = {
  // 获取用户列表
  getList: (params = {}) => {
    return request.get<ApiResponse<UserItem[]>>("/admin/user", { params });
  },

  // 获取单个用户
  getDetail: (id: number) => {
    return request.get<ApiResponse<UserItem>>(`/admin/user/${id}`);
  },

  // 创建用户
  create: (data: UserForm) => {
    return request.post<ApiResponse<UserItem>>("/admin/user", data);
  },

  // 更新用户
  update: (id: number, data: Partial<UserForm>) => {
    return request.put<ApiResponse<UserItem>>(`/admin/user/${id}`, data);
  },

  // 删除用户
  delete: (id: number) => {
    return request.delete<ApiResponse<void>>(`/admin/user/${id}`);
  },

  // 批量删除
  batchDelete: (ids: number[]) => {
    return request.delete<ApiResponse<void>>("/admin/user/batch", {
      data: { ids },
    });
  },
};
```

### 路由配置规范

```typescript
// router/modules/user.ts
import type { RouteRecordRaw } from "vue-router";

const userRoutes: RouteRecordRaw[] = [
  {
    path: "/user",
    name: "User",
    component: () => import("@/layouts/AdminLayout.vue"),
    meta: {
      title: "用户管理",
      icon: "user",
      auth: true,
    },
    children: [
      {
        path: "list",
        name: "UserList",
        component: () => import("@/modules/user/views/UserList.vue"),
        meta: {
          title: "用户列表",
          keepAlive: true,
        },
      },
      {
        path: "create",
        name: "UserCreate",
        component: () => import("@/modules/user/views/UserForm.vue"),
        meta: {
          title: "新增用户",
        },
      },
    ],
  },
];

export default userRoutes;
```

### 表单处理规范

```vue
<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" placeholder="请输入用户名" />
    </el-form-item>

    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" type="email" placeholder="请输入邮箱" />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      <el-button @click="handleReset"> 重置 </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";

const formRef = ref<FormInstance>();

const form = reactive<UserForm>({
  username: "",
  email: "",
  password: "",
  confirm_password: "",
});

const rules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, max: 20, message: "用户名长度为 3-20 位", trigger: "blur" },
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" },
  ],
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    // 提交表单
    await userApi.create(form);
    ElMessage.success("用户创建成功");
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

const handleReset = () => {
  formRef.value?.resetFields();
};
</script>
```

### 样式规范

```scss
// 使用 SCSS
.user-management {
  padding: 16px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  &__content {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 16px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 8px;

    &__header {
      flex-direction: column;
      gap: 8px;
    }
  }
}

// CSS 变量使用
.custom-button {
  --button-color: #409eff;
  --button-hover-color: #66b1ff;

  background-color: var(--button-color);

  &:hover {
    background-color: var(--button-hover-color);
  }
}
```

### 性能优化规范

1. **懒加载**: 使用 `defineAsyncComponent` 或路由懒加载
2. **虚拟列表**: 大数据量使用虚拟滚动
3. **防抖节流**: 搜索和高频操作使用防抖节流
4. **缓存优化**: 合理使用 keep-alive
5. **代码分割**: 按模块分割代码

### 错误处理规范

```typescript
// 全局错误处理
import { createApp } from "vue";
import { ElMessage } from "element-plus";

const app = createApp(App);

app.config.errorHandler = (err, vm, info) => {
  console.error("Vue Error:", err, info);
  ElMessage.error("系统错误，请稍后重试");
};

// API 错误处理
const request = axios.create({
  baseURL: "/api",
  timeout: 10000,
});

request.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // 未授权处理
      router.push("/login");
    } else if (error.response?.status >= 500) {
      ElMessage.error("服务器错误");
    }
    return Promise.reject(error);
  }
);
```

### 测试规范

```typescript
// UserList.test.ts
import { mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import UserList from "@/modules/user/views/UserList.vue";

describe("UserList", () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(UserList, {
      global: {
        plugins: [createPinia()],
        stubs: {
          "ma-pro-table": true,
          "ma-search": true,
        },
      },
    });
  });

  it("应该正确渲染组件", () => {
    expect(wrapper.find(".user-list").exists()).toBe(true);
  });

  it("应该正确处理搜索事件", async () => {
    const searchParams = { username: "test" };
    await wrapper.vm.handleSearch(searchParams);

    expect(wrapper.vm.tableRef.refresh).toHaveBeenCalledWith(searchParams);
  });
});
```

### 国际化规范

```typescript
// locales/zh-CN.ts
export default {
  user: {
    title: '用户管理',
    list: '用户列表',
    create: '新增用户',
    edit: '编辑用户',
    delete: '删除用户',
    form: {
      username: '用户名',
      email: '邮箱',
      password: '密码'
    },
    rules: {
      usernameRequired: '请输入用户名',
      emailRequired: '请输入邮箱'
    }
  }
}

// 组件中使用
<template>
  <h1>{{ $t('user.title') }}</h1>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const title = computed(() => t('user.title'))
</script>
```
