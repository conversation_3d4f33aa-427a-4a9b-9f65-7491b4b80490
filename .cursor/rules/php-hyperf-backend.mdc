---
globs: app/**/*.php,config/**/*.php,databases/**/*.php,tests/**/*.php
description: PHP/Hyperf 后端开发规范
---

# PHP/Hyperf 后端开发规范

## 文件引用规范

- 控制器文件: [app/Http/Admin/](mdc:app/Http/Admin/)
- 模型文件: [app/Model/](mdc:app/Model/)
- 服务层: [app/Service/](mdc:app/Service/)
- 仓库层: [app/Repository/](mdc:app/Repository/)
- 配置文件: [config/](mdc:config/)

## 代码规范

### 类和文件结构

```php
<?php

declare(strict_types=1);

namespace App\Http\Admin;

use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\GetMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Mine\MineController;

#[Controller(prefix: '/admin/user')]
class UserController extends MineController
{
    #[GetMapping(path: 'index')]
    public function index(RequestInterface $request): array
    {
        // 控制器逻辑
    }
}
```

### 控制器规范

1. **继承基类**: 继承 `Mine\MineController`
2. **注解路由**: 使用 `#[Controller]` 和 `#[GetMapping]` 等注解
3. **方法命名**: 使用 camelCase，如 `getUserList()`
4. **参数注入**: 利用依赖注入获取服务和请求对象
5. **返回类型**: 明确声明返回类型

### 模型规范

```php
<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\Database\Model\Model;
use Mine\MineModel;

class User extends MineModel
{
    protected ?string $table = 'users';

    protected array $fillable = [
        'username',
        'email',
        'password',
    ];

    protected array $hidden = [
        'password',
    ];

    protected array $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
```

1. **继承基类**: 继承 `Mine\MineModel`
2. **表名定义**: 明确指定 `$table` 属性
3. **批量赋值**: 定义 `$fillable` 数组
4. **隐藏字段**: 使用 `$hidden` 隐藏敏感字段
5. **类型转换**: 使用 `$casts` 进行数据类型转换

### 服务层规范

```php
<?php

declare(strict_types=1);

namespace App\Service;

use App\Model\User;
use App\Repository\UserRepository;
use Mine\MineService;

class UserService extends MineService
{
    public function __construct(
        protected UserRepository $repository
    ) {}

    public function getUserList(array $params = []): array
    {
        return $this->repository->getList($params);
    }

    public function createUser(array $data): User
    {
        return $this->repository->create($data);
    }
}
```

1. **继承基类**: 继承 `Mine\MineService`
2. **依赖注入**: 通过构造函数注入仓库层
3. **业务逻辑**: 处理复杂的业务逻辑
4. **数据验证**: 在服务层进行数据验证

### 仓库层规范

```php
<?php

declare(strict_types=1);

namespace App\Repository;

use App\Model\User;
use Mine\MineRepository;

class UserRepository extends MineRepository
{
    public function __construct(
        protected User $model
    ) {}

    public function getList(array $params = []): array
    {
        $query = $this->model->newQuery();

        if (!empty($params['username'])) {
            $query->where('username', 'like', "%{$params['username']}%");
        }

        return $query->paginate($params['page_size'] ?? 15)->toArray();
    }
}
```

1. **继承基类**: 继承 `Mine\MineRepository`
2. **模型注入**: 注入对应的模型类
3. **查询构建**: 使用查询构建器构建复杂查询
4. **分页处理**: 统一的分页处理方式

### 数据验证

```php
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

public function store(RequestInterface $request, ValidatorFactoryInterface $validatorFactory)
{
    $validator = $validatorFactory->make(
        $request->all(),
        [
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:6',
        ],
        [
            'username.required' => '用户名不能为空',
            'username.unique' => '用户名已存在',
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
            'password.required' => '密码不能为空',
            'password.min' => '密码至少6位',
        ]
    );

    if ($validator->fails()) {
        throw new ValidationException($validator);
    }
}
```

### 异常处理

```php
<?php

declare(strict_types=1);

namespace App\Exception;

use Mine\MineException;

class UserNotFoundException extends MineException
{
    protected int $code = 404;
    protected string $message = '用户不存在';
}
```

### 中间件使用

```php
#[Middleware(AuthMiddleware::class)]
#[Controller(prefix: '/admin/user')]
class UserController extends MineController
{
    // 控制器方法
}
```

### 事件系统

```php
// 定义事件
namespace App\Event;

class UserCreated
{
    public function __construct(
        public User $user
    ) {}
}

// 监听器
namespace App\Listener;

use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;

#[Listener]
class UserCreatedListener implements ListenerInterface
{
    public function listen(): array
    {
        return [UserCreated::class];
    }

    public function process(object $event): void
    {
        // 处理用户创建事件
    }
}
```

### 缓存使用

```php
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Cache\Annotation\CacheEvict;

class UserService
{
    #[Cacheable(prefix: 'user', ttl: 3600)]
    public function getUserById(int $id): ?User
    {
        return $this->repository->find($id);
    }

    #[CacheEvict(prefix: 'user', all: true)]
    public function updateUser(int $id, array $data): User
    {
        return $this->repository->update($id, $data);
    }
}
```

### 队列任务

```php
<?php

declare(strict_types=1);

namespace App\Job;

use Hyperf\AsyncQueue\Job;

class SendEmailJob extends Job
{
    public function __construct(
        protected string $email,
        protected string $subject,
        protected string $content
    ) {}

    public function handle(): void
    {
        // 发送邮件逻辑
    }
}
```

### 数据库迁移

```php
<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('email')->unique()->comment('邮箱');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->comment('密码');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['username', 'email']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
}
```

### 测试规范

```php
<?php

declare(strict_types=1);

namespace HyperfTests\Feature;

use HyperfTests\HttpTestCase;

class UserControllerTest extends HttpTestCase
{
    public function testGetUserList(): void
    {
        $response = $this->get('/admin/user');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'code',
            'message',
            'data' => [
                'current_page',
                'data',
                'total',
            ],
        ]);
    }
}
```

### 性能优化建议

1. **数据库优化**: 使用索引、避免 N+1 查询
2. **缓存策略**: 合理使用缓存减少数据库查询
3. **协程使用**: 利用协程处理 I/O 密集型操作
4. **连接池**: 使用连接池管理数据库连接
5. **内存优化**: 避免内存泄漏，合理使用内存

### 安全规范

1. **SQL 注入**: 使用 ORM 或预处理语句
2. **XSS 防护**: 对输出进行转义
3. **CSRF 防护**: 验证请求来源
4. **认证授权**: 实施严格的认证和授权机制
5. **敏感数据**: 加密存储敏感信息
