<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('baccarat_lottery_log', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->comment('百家乐开奖日志表');
            $table->foreignId('terrace_deck_id')->constrained('baccarat_terrace_deck')->onDelete('cascade')->comment('牌靴ID');
            $table->bigInteger('issue')->comment('期号（局号）');
            $table->string('result')->comment('开奖数据')->nullable();
            $table->string('transformation_result')->comment('开奖结果：B=庄赢，P=闲赢，T=和局')->nullable();
            $table->string('banker_cards', 20)->comment('庄家牌型')->nullable();
            $table->string('player_cards', 20)->comment('闲家牌型')->nullable();
            $table->tinyInteger('banker_points')->comment('庄家点数')->nullable();
            $table->tinyInteger('player_points')->comment('闲家点数')->nullable();
            $table->json('raw_data')->comment('原始开奖数据')->nullable();
            $table->string('remark', 255)->comment('备注说明')->nullable();

            $table->datetimes();

            // 索引优化
            $table->unique('issue');
            $table->index(['terrace_deck_id', 'issue']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('baccarat_lottery_log');
    }
};
