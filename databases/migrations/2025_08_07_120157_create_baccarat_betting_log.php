<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('baccarat_betting_log', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->comment('百家乐投注日志表');

            $table->foreignId('lottery_log_id')->constrained('baccarat_lottery_log')->onDelete('cascade')->comment('开奖记录ID');
            $table->foreignId('terrace_deck_id')->constrained('baccarat_terrace_deck')->onDelete('cascade')->comment('牌靴ID');
            $table->bigInteger('issue')->comment('期号（冗余字段，便于查询）');

            $table->string('betting_type', 10)->comment('投注类型：B=庄，P=闲，T=和，BP=庄闲对，PP=闲对');
            $table->tinyInteger('betting_result')->comment('投注结果：0=输，1=赢，2=和局退还')->nullable();

            $table->decimal('confidence', 5, 2)->comment('可信度（0-100）')->nullable();

            $table->json('response')->comment('投注结果')->nullable();

            $table->datetimes();

            // 索引优化
            $table->index(['lottery_log_id', 'betting_result']);
            $table->index(['terrace_deck_id', 'issue']);
            $table->index(['betting_result', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('baccarat_betting_log');
    }
};
