<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('baccarat_terrace', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->comment('百家乐台桌表');
            $table->string('code', 60)->unique()->comment('台桌标识码');
            $table->string('title', 60)->comment('台桌名称');
            $table->tinyInteger('status')->default(1)->comment('状态：0=禁用，1=启用');
            $table->integer('sort')->default(0)->comment('排序权重');
            $table->string('remark', 255)->comment('备注说明')->nullable();

            $table->datetimes();

            // 索引优化
            $table->index(['status', 'sort']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('baccarat_terrace');
    }
};
