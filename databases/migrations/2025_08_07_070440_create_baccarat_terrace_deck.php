<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('baccarat_terrace_deck', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->comment('百家乐牌靴表');
            $table->foreignId('terrace_id')->constrained('baccarat_terrace')->onDelete('cascade')->comment('所属台桌ID');
            $table->integer('deck_number')->comment('牌靴编号');
            $table->tinyInteger('status')->default(1)->comment('状态：0=已结束，1=进行中，2=暂停');
            $table->string('lottery_sequence', 500)->comment('开奖序列（B=庄，P=闲，T=和）')->nullable();
            $table->integer('total_rounds')->default(0)->comment('总局数');
            $table->string('remark', 255)->comment('备注说明')->nullable();

            $table->datetimes();

            // 索引优化
            $table->unique(['terrace_id', 'deck_number']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('baccarat_terrace_deck');
    }
};
