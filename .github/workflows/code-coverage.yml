name: Code Coverage Upload

on:
  push:
    paths:
      - 'app/**'
      - 'config/**'
      - 'composer.*'
      - 'databases/**'
      - 'tests/**'
      - '.github/ci/**'
      - '.github/workflows/code-coverage.yml'
  pull_request:
    paths:
      - 'app/**'
      - 'config/**'
      - 'composer.*'
      - 'databases/**'
      - 'tests/**'
      - '.github/ci/**'
      - '.github/workflows/code-coverage.yml'
  schedule:
    - cron: '0 2 * * *'

jobs:
  tests:
    name: build Code coverage report
    runs-on: "${{ matrix.os }}"
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php-version: [ '8.3' ]
        sw-version: [ 'master' ]
        exclude:
          - php-version: '8.3'
            sw-version: 'v5.0.3'
      max-parallel: 20
      fail-fast: false
    env:
      SW_VERSION: ${{ matrix.sw-version }}
      MYSQL_VERSION: '8.0'
      PGSQL_VERSION: '14'
      PHP_EXTENSIONS: redis, pdo, pdo_mysql, sqlsrv, pdo_sqlsrv, pdo_odbc, bcmath, swoole-${{ matrix.sw-version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: ${{ env.PHP_EXTENSIONS }}
          ini-values: opcache.enable_cli=0
          coverage: xdebug
      - name: Set Swoole Shortname
        run: |
          sudo sh -c "echo swoole.use_shortname='Off' >> /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          php --ri swoole
          php --ri xdebug
      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "composer_cache_dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      - name: Setup composer cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.composer_cache_dir }}
          key: ${{ matrix.os }}-php-${{ matrix.php-version }}-swoole-${{ matrix.sw-version }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ matrix.os }}-php-${{ matrix.php-version }}-swoole-${{ matrix.sw-version }}-composer-
      - name: Setup Swoole
        run: |
          sudo sh -c "echo swoole.use_shortname='Off' >> /etc/php/${{ matrix.php-version }}/cli/conf.d/swoole.ini"
          sudo sh -c "echo 'swoole.enable_fiber_mock=On' >> /etc/php/${{ matrix.php-version }}/cli/conf.d/20-xdebug.ini"
          php --ri swoole
          php --ri xdebug
      - name: Setup Packages
        run: ./.github/ci/requirement.install.sh
      - name: Run PHPStan
        run: ./.github/ci/run.check.sh
      - name: Setup Services
        run: ./.github/ci/setup.services.sh
      - name: Setup Mysql
        run: bash ./.github/ci/setup.mysql.sh
      - name: Setup PostgreSQL
        run: bash ./.github/ci/setup.pgsql.sh
      - name: Run Scripts Before Test
        run: cp .github/ci/.env.example .env
      - name: Print PHP Environments
        run: |
          php -i
          php -m
      - name: Run Mysql Test Cases
        env:
          DB_DRIVER: mysql
          DB_HOST: 127.0.0.1
          DB_DATABASE: mineadmin
        run: ./.github/ci/run.code-coverage.sh
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4-beta
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: "tests/coverage/index.xml"