name: Sync Gitee Repository

on:
  schedule:
    - cron: '* 1 * * *'

jobs:
  sync:
    runs-on: ubuntu-latest
    env:
      SSH_PRIVATE_KEY: ${{ secrets.SSH_KEY }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Private Key
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "StrictHostKeyChecking no" >> ~/.ssh/config
      - name: Push Mirror
        env:
          SOURCE_REPO: 'https://github.com/mineadmin/mineadmin.git'
          DESTINATION_REPO: '*************:mineadmin/mineadmin.git'
        run: |
          git clone --mirror "$SOURCE_REPO" && cd `basename "$SOURCE_REPO"`
          git remote set-url --push origin "$DESTINATION_REPO"
          git fetch -p origin
          git for-each-ref --format 'delete %(refname)' refs/pull | git update-ref --stdin
          git push --mirror