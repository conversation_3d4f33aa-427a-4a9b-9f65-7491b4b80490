labelPRBasedOnFilePath:
  Permission:
    - app/Model/Permission/**/*
    - app/Service/Permission/**/*
    - app/Repository/Permission/**/*
  Http:
    - app/Http/**/*
  Model:
    - app/Model/**/*
  Service:
    - app/Service/**/*
  Repository:
    - app/Repository/**/*
  Schema:
    - app/Schema/**/*
  Command:
    - app/Command/**/*
  Exception:
    - app/Exception/**/*
  Authentication:
    - app/Http/CurrentUser.php
    - app/Service/PassportService.php
  Attachment:
    - app/Model/Attachment.php
    - app/Service/AttachmentService.php
    - app/Repository/AttachmentRepository.php
    - app/Schema/AttachmentSchema.php
  Logging:
    - app/Model/UserLoginLog.php
    - app/Model/UserOperationLog.php
    - app/Service/Logstash/**/*
    - app/Repository/Logstash/**/*
  Frontend:
    - web/**/*
  Config:
    - config/**/*
  Database:
    - databases/**/*
  UnitTest:
    - tests/**/*
  ChangeLog:
    - CHANGELOG*.md
    - web/CHANGELOG.md
  Composer:
    - composer.json
    - composer.lock
  Docker:
    - Dockerfile
    - docker-compose.yml
    - deploy.test.yml
  Documentation:
    - README*.md
    - CONTRIBUTING.md
    - SECURITY.md
    - LICENSE
  GitHub:
    - .github/**/*

firstPRWelcomeComment: >
  Thanks for opening this pull request! Please check out our [contributing guidelines](https://github.com/mineadmin/MineAdmin/blob/master/CONTRIBUTING.md).
  感谢您开启此拉取请求！请查看我们的 [贡献者指南](https://github.com/mineadmin/MineAdmin/blob/master/CONTRIBUTING.md#L51)。
# Comment to be posted to congratulate user on their first merged PR
firstPRMergeComment: >
  Awesome work, congrats on your first merged pull request!
  祝贺你的第一个拉取请求被合并！

# Comment to be posted to on first time issues
firstIssueWelcomeComment: >
  Thanks for opening your first issue here! Be sure to follow the issue template!
  感谢您在这里反馈第一个问题！请务必遵循问题模板！