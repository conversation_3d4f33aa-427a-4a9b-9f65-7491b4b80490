# Magic behaviour with __get, __set, __call and __callStatic is not exactly static analyser-friendly :)
# Fortunately, You can ingore it by the following config.
#
# vendor/bin/phpstan analyse app --memory-limit 200M -l 0
#
parameters:
  reportUnmatchedIgnoredErrors: false
  excludePaths:
    - tests/
  ignoreErrors:
    - '#Static call to instance method Hyperf\\HttpServer\\Router\\Router::[a-zA-Z0-9\\_]+\(\)#'
    - '#Static call to instance method Hyperf\\DbConnection\\Db::[a-zA-Z0-9\\_]+\(\)#'
    - '#Constant BASE_PATH not found.#'
    - '#Call to an undefined static method App\\Model.*#'
    - '#Call to an undefined static method T of Hyperf\\.*#'
    - '#.with no value type specified in iterable type array.*#'
    - '#.return type specified.*#'
    - '#Access to an undefined property.*#'
    - '#Call to an undefined static method App\\Kernel\\Casbin\\Rule\\Rule::getModel\(\)#'