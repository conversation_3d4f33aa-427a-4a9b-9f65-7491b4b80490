<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */
use App\Service\PassportService;
use Mine\JwtAuth\Interfaces\CheckTokenInterface;
use Mine\Upload\Factory;
use Mine\Upload\UploadInterface;
use App\Service\Platform\Bacc\Contract\AccountManagerInterface;
use App\Service\Platform\Bacc\AccountManager;
use App\Service\Platform\Bacc\Contract\PlatformClientInterface;
use App\Service\Platform\Bacc\SaloonBaccClient;
return [
    UploadInterface::class => Factory::class,
    CheckTokenInterface::class => PassportService::class,
    AccountManagerInterface::class => AccountManager::class,
    PlatformClientInterface::class => SaloonBaccClient::class,
];
