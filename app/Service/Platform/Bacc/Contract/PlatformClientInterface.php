<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Contract;

use App\Service\Platform\Bacc\BaccResponse;

/**
 * 平台客户端接口
 */
interface PlatformClientInterface
{
    /**
     * 执行预测计算
     * @param array $data 预测数据
     * @return BaccResponse 预测结果
     */
    public function calculate(array $data): BaccResponse;

    /**
     * 获取仪表板信息
     * @return array 仪表板数据
     */
    public function dashboard(): array;
}
