<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Contract;

use App\Service\Platform\Bacc\Account;

/**
 * 账号管理接口
 */
interface AccountManagerInterface
{
    /**
     * 获取下一个可用账号
     * @return Account|null 可用账号或 null
     */
    public function getNextAccount(): ?Account;

    /**
     * 标记账号失败
     * @param Account $account 失败的账号
     * @return void
     */
    public function markFailure(Account $account): void;

    /**
     * 重置所有账号状态
     * @return void
     */
    public function resetAllAccounts(): void;
}
