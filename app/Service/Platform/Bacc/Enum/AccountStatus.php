<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Enum;

/**
 * 账号状态枚举
 */
enum AccountStatus: string
{
    case HEALTHY = 'healthy';
    case FAILED = 'failed';
    case DISABLED = 'disabled';

    public function isHealthy(): bool
    {
        return $this === self::HEALTHY;
    }

    public function isFailed(): bool
    {
        return $this === self::FAILED;
    }

    public function isDisabled(): bool
    {
        return $this === self::DISABLED;
    }
}
