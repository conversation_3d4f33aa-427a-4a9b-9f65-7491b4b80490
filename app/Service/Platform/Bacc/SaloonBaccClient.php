<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc;

use App\Service\Platform\Bacc\Contract\AccountManagerInterface;
use App\Service\Platform\Bacc\Contract\PlatformClientInterface;
use App\Service\Platform\Bacc\Exception\NoAvailableAccountException;
use App\Service\Platform\Bacc\Saloon\BaccConnectorPool;
use App\Service\Platform\Bacc\Saloon\Requests\CalculateRequest;
use App\Service\Platform\Bacc\Saloon\Requests\DashboardRequest;
use App\Service\Platform\Bacc\Account;
use App\Service\Platform\Bacc\BaccResponse;
use App\Service\Platform\Bacc\Saloon\BaccConnector;
use GuzzleHttp\Exception\InvalidArgumentException;
use Hyperf\Di\Annotation\Inject;
use Saloon\Http\Response;
use Saloon\Exceptions\Request\Statuses\TooManyRequestsException;
use Psr\Log\LoggerInterface;
use Saloon\Exceptions\SaloonException;
/**
 * 使用 Saloon 的 Bacc 平台客户端
 */
class SaloonBaccClient implements PlatformClientInterface
{
    public function __construct(
        private readonly BaccConnectorPool $baccConnectorPool,
        private readonly AccountManagerInterface $accountManager,
        private readonly LoggerInterface $logger
    ) {}

    public function calculate(array $data): BaccResponse
    {
        $request = new CalculateRequest(['dataArray'=>$data, 'turboMode' => true]);
        $response = $this->sendRequestWithRetry($request);

        $responseData = $response->json();

        if (empty($responseData['message'])) {
            throw new InvalidArgumentException('Empty message in response: ' . $response->body());
        }

        return new BaccResponse($responseData);
    }

    public function dashboard(): array
    {
        $request = new DashboardRequest();
        $response = $this->sendRequestWithRetry($request);

        return $response->json();
    }

    /**
     * 发送带重试机制的请求
     */
    private function sendRequestWithRetry(\Saloon\Http\Request $request): Response
    {
        

        while (true) {
            $account = $this->getAvailableAccount();

            try {
                // 使用账号配置的连接器发送请求

                /**
                 * @var BaccConnector $connector
                 */
                $connector = $this->baccConnectorPool->getConnector();
                $connector->withCookies($account->getCookieJar());
                $connector->withLogger($this->logger);
                $response = $connector->send($request);

                // Saloon 已经会自动处理 HTTP 错误状态码
                return $response;
            } catch (TooManyRequestsException  $e) {

                $this->logger->error("{$account->getName()} {$e->getMessage()}");

            }catch(SaloonException $e){
                $account->markFailure();

                $this->logger->error("{$e->getMessage()}");
            }

        }
    }

    /**
     * 获取可用账号
     */
    private function getAvailableAccount(): Account
    {
        $account = $this->accountManager->getNextAccount();

        if ($account === null) {
            throw new NoAvailableAccountException('No available accounts');
        }

        return $account;
    }
}
