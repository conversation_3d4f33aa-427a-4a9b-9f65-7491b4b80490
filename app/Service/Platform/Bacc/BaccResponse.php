<?php

namespace App\Service\Platform\Bacc;

use App\Service\LotteryResult;
use App\Service\Enums\Bet;
class BaccResponse
{
    private  ?Bet $bet;        // 投注方向
    protected ?int $credibility = null;
    /**
     * @param array $data
     */
    public function __construct(
        public readonly array $data
    ) {
    
    }

    public function getCredibility(): ?int
    {
        return $this->credibility;
    }


    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_THROW_ON_ERROR);
    }

    public function toArray(): array
    {
        return [
            $this->data
        ];
    }

    /**
     * 获取投注方向
     */
    public function getBet(): ?Bet
    {
        if(empty($this->data['turboStats']['predictionSide'])){
            $this->bet =  null;
        }else{
            $this->bet = Bet::from($this->data['turboStats']['predictionSide']);
        }
        return $this->bet;
    }

    /**
     * 获取可信度数值
     */
    public function getConfidence():null|int|string
    {
        return $this->credibility ??= $this->data['turboStats']['predictionPercentage'] ?? 0;
    }


    /**
     * 是否投注庄家
     */
    public function isBanker(): bool
    {
        return $this->getBet()?->getOpposite() === LotteryResult::BANKER;
    }

    /**
     * 是否投注闲家
     */
    public function isPlayer(): bool
    {
        return $this->getBet()?->getOpposite() === LotteryResult::PLAYER;
    }

    /**
     * 是否高可信度
     */
    public function isHigh(): bool
    {
        return $this->credibility >= 80;
    }

    /**
     * 是否中等可信度
     */
    public function isMedium(): bool
    {
        return $this->credibility >= 50 && $this->credibility < 80;
    }

    /**
     * 是否较低可信度
     */
    public function isAlmost(): bool
    {
        return $this->credibility >= 0 && $this->credibility < 50;
    }
}