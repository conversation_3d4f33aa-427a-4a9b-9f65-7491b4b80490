<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Saloon;

use Saloon\Contracts\Sender;
use Saloon\Http\Connector;
use Saloon\Traits\Plugins\AcceptsJson;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;
use <PERSON>jiajia\SaloonphpLogsPlugin\HasLogger;
use Weijiajia\SaloonphpLogsPlugin\Contracts\HasLoggerInterface;
use Weijiajia\SaloonphpCookiePlugin\Contracts\CookieJarInterface;
use Weijiajia\SaloonphpCookiePlugin\HasCookie;

/**
 * Bacc 平台 Saloon 连接器
 */
class BaccConnector extends Connector implements HasLoggerInterface, CookieJarInterface
{
    use AcceptsJson;
    use AlwaysThrowOnErrors;
    use HasLogger;
    use HasCookie;

    /**
     * 解析基础 URL
     */
    public function resolveBaseUrl(): string
    {
        return 'https://www.bacc.bot';
    }

    /**
     * 默认请求头
     */
    protected function defaultHeaders(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36',
        ];

        return $headers;
    }

    /**
     * 默认配置
     */
    protected function defaultConfig(): array
    {
        $config = [
            'timeout' => 30,
            'connect_timeout' => 30,
            'verify' => false,
        ];

        return $config;
    }

    /**
     * 创建自定义的协程化 Sender
     */
    protected function defaultSender(): Sender
    {
        return new HyperfGuzzleSender([
            'base_uri' => $this->resolveBaseUrl(),
            'timeout' => 30,
            'connect_timeout' => 30,
            'verify' => false,
        ]);
    }


    /**
     * 获取协程化的 Sender
     */
    public function getCoroutineSender(): ?HyperfGuzzleSender
    {
        $sender = $this->defaultSender();

        return $sender instanceof HyperfGuzzleSender ? $sender : null;
    }

    /**
     * 检查是否在协程环境中运行
     */
    public function isInCoroutine(): bool
    {
        $sender = $this->getCoroutineSender();

        return $sender?->isInCoroutine() ?? false;
    }

    /**
     * 获取当前协程 ID（调试用）
     */
    public function getCoroutineId(): int
    {
        $sender = $this->getCoroutineSender();

        return $sender?->getCoroutineId() ?? -1;
    }
}
