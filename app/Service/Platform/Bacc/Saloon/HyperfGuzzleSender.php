<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Saloon;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\HttpFactory;
use GuzzleHttp\RequestOptions;
use Hyperf\Coroutine\Coroutine;
use Hyperf\Guzzle\CoroutineHandler;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Saloon\Config;
use Saloon\Contracts\Sender;
use Saloon\Data\FactoryCollection;
use Saloon\Http\PendingRequest;
use Saloon\Http\Response;
use Saloon\Http\Senders\Factories\GuzzleMultipartBodyFactory;
use Swoole\Runtime;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\TransferException;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Http\Senders\GuzzleSender;

/**
 * Hyperf 协程化的 Saloon Sender
 * 
 * 基于 Saloon 原生 GuzzleSender 的协程化实现
 * 确保在 Hyperf 协程环境中使用协程化的 Guzzle 客户端
 */
class HyperfGuzzleSender extends GuzzleSender
{
    /**
     * The Guzzle client.
     */
    protected Client $client;

    /**
     * Guzzle's Handler Stack.
     */
    protected HandlerStack $handlerStack;

    /**
     * Constructor - Create the HTTP client with coroutine support
     */
    public function __construct(array $config = [])
    {
        $this->client = $this->createCoroutineGuzzleClient($config);
    }

    /**
     * Get the factory collection
     */
    public function getFactoryCollection(): FactoryCollection
    {
        $factory = new HttpFactory();

        return new FactoryCollection(
            requestFactory: $factory,
            uriFactory: $factory,
            streamFactory: $factory,
            responseFactory: $factory,
            multipartBodyFactory: new GuzzleMultipartBodyFactory(),
        );
    }

    /**
     * Send a synchronous request.
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Saloon\Exceptions\Request\FatalRequestException
     */
    public function send(PendingRequest $pendingRequest): Response
    {
        $request = $pendingRequest->createPsrRequest();
        $requestOptions = $pendingRequest->config()->all();

        try {
            $guzzleResponse = $this->client->send($request, $requestOptions);

            return $this->createResponse($guzzleResponse, $pendingRequest, $request);
        } catch (ConnectException $exception) {
            // ConnectException means a network exception has happened, like Guzzle
            // not being able to connect to the host.

            throw new FatalRequestException($exception, $pendingRequest);
        } catch (RequestException $exception) {
            // Sometimes, Guzzle will throw a RequestException without a response. This
            // means that it was fatal, so we should still throw a fatal request exception.

            $guzzleResponse = $exception->getResponse();

            if (is_null($guzzleResponse)) {
                throw new FatalRequestException($exception, $pendingRequest);
            }

            return $this->createResponse($guzzleResponse, $pendingRequest, $request, $exception);
        }
    }

    /**
     * Send an asynchronous request
     */
    public function sendAsync(PendingRequest $pendingRequest): PromiseInterface
    {
        $request = $pendingRequest->createPsrRequest();
        $requestOptions = $pendingRequest->config()->all();

        $promise = $this->client->sendAsync($request, $requestOptions);

        return $this->processPromise($request, $promise, $pendingRequest);
    }

    /**
     * Create a new coroutine-enabled Guzzle client
     */
    protected function createCoroutineGuzzleClient(array $customConfig = []): Client
    {
        // Create coroutine-enabled handler stack
        $this->handlerStack = $this->createCoroutineHandlerStack();

        // Merge default config with custom config
        $defaultConfig = [
            RequestOptions::CRYPTO_METHOD => Config::$defaultTlsMethod,
            RequestOptions::CONNECT_TIMEOUT => Config::$defaultConnectionTimeout,
            RequestOptions::TIMEOUT => Config::$defaultRequestTimeout,
            RequestOptions::HTTP_ERRORS => true,
            'handler' => $this->handlerStack,
        ];

        $config = array_merge($defaultConfig, $customConfig);

        return new Client($config);
    }

    /**
     * 创建协程化的处理器栈
     */
    protected function createCoroutineHandlerStack(): HandlerStack
    {
        // 检查是否在协程环境中
        if (!Coroutine::inCoroutine()) {
            throw new \Exception('Not in coroutine environment');
        }

        // 未启用 native curl hook，使用 Hyperf 的协程处理器
        return HandlerStack::create(new CoroutineHandler());
    }

    /**
     * Update the promise provided by Guzzle.
     */
    protected function processPromise(RequestInterface $psrRequest, PromiseInterface $promise, PendingRequest $pendingRequest): PromiseInterface
    {
        return $promise
            ->then(
                function (ResponseInterface $guzzleResponse) use ($psrRequest, $pendingRequest) {
                    // Instead of the promise returning a Guzzle response, we want to return
                    // a Saloon response.

                    return $this->createResponse($guzzleResponse, $pendingRequest, $psrRequest);
                },
                function (TransferException $guzzleException) use ($pendingRequest, $psrRequest) {
                    // When the exception wasn't a RequestException, we'll throw a fatal
                    // exception as this is likely a ConnectException, but it will
                    // catch any new ones Guzzle release.

                    if (! $guzzleException instanceof RequestException) {
                        throw new FatalRequestException($guzzleException, $pendingRequest);
                    }

                    // Sometimes, Guzzle will throw a RequestException without a response. This
                    // means that it was fatal, so we should still throw a fatal request exception.

                    $guzzleResponse = $guzzleException->getResponse();

                    if (is_null($guzzleResponse)) {
                        throw new FatalRequestException($guzzleException, $pendingRequest);
                    }

                    // Otherwise we'll create a response to convert into an exception.
                    // This will run the exception through the exception handlers
                    // which allows the user to handle their own exceptions.

                    $response = $this->createResponse($guzzleResponse, $pendingRequest, $psrRequest, $guzzleException);

                    // Throw the exception our way

                    $exception = $response->toException();
                    if ($exception) {
                        throw $exception;
                    }

                    return $response;
                }
            );
    }

    /**
     * Create a response.
     */
    protected function createResponse(ResponseInterface $psrResponse, PendingRequest $pendingRequest, RequestInterface $psrRequest, ?Exception $exception = null): Response
    {
        /** @var class-string<\Saloon\Http\Response> $responseClass */
        $responseClass = $pendingRequest->getResponseClass();

        return $responseClass::fromPsrResponse($psrResponse, $pendingRequest, $psrRequest, $exception);
    }

    /**
     * Add a middleware to the handler stack.
     *
     * @return $this
     */
    public function addMiddleware(callable $callable, string $name = ''): static
    {
        $this->handlerStack->push($callable, $name);

        return $this;
    }

    /**
     * Overwrite the entire handler stack.
     *
     * @return $this
     */
    public function setHandlerStack(HandlerStack $handlerStack): static
    {
        $this->handlerStack = $handlerStack;

        // Recreate the client with new handler stack
        $this->client = new Client(array_merge(
            $this->client->getConfig(),
            ['handler' => $handlerStack]
        ));

        return $this;
    }

    /**
     * Get the handler stack.
     */
    public function getHandlerStack(): HandlerStack
    {
        return $this->handlerStack;
    }

    /**
     * Get the Guzzle client
     */
    public function getGuzzleClient(): Client
    {
        return $this->client;
    }

    /**
     * 检查是否在协程环境中运行
     */
    public function isInCoroutine(): bool
    {
        return Coroutine::inCoroutine();
    }

    /**
     * 获取当前协程 ID（调试用）
     */
    public function getCoroutineId(): int
    {
        return Coroutine::id();
    }

    /**
     * 获取协程环境详细信息
     */
    public function getCoroutineInfo(): array
    {
        $hookFlags = Runtime::getHookFlags();

        return [
            'in_coroutine' => Coroutine::inCoroutine(),
            'coroutine_id' => Coroutine::id(),
            'hook_flags' => $hookFlags,
            'native_curl_hooked' => ($hookFlags & SWOOLE_HOOK_NATIVE_CURL) !== 0,
            'handler_type' => $this->getHandlerType(),
        ];
    }

    /**
     * 获取当前使用的处理器类型
     */
    private function getHandlerType(): string
    {
        $handler = $this->handlerStack->resolve();

        if ($handler instanceof CoroutineHandler) {
            return 'CoroutineHandler';
        }

        return get_class($handler);
    }
}
