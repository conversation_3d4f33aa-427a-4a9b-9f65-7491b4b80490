<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Saloon\Requests;

use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

/**
 * 预测计算请求
 */
class CalculateRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        private readonly array $data
    ) {}

    public function resolveEndpoint(): string
    {
        return '/api/ai7';
    }

    protected function defaultBody(): array
    {
        return $this->data;
    }
}
