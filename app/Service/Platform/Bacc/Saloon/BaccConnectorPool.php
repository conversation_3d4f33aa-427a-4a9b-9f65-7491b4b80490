<?php

declare(strict_types=1);

namespace App\Service\Platform\Bacc\Saloon;

use Hyperf\Pool\Pool;
use Hyperf\Pool\SimplePool\PoolFactory;
use Hyperf\Di\Annotation\Inject;
use Psr\Log\LoggerInterface;

/**
 * Bacc 连接器连接池
 * 
 * 管理 Saloon 连接器的池化，提高性能
 */
class BaccConnectorPool
{
    private Pool $pool;

    public function __construct(
        private readonly PoolFactory $poolFactory,
        private readonly LoggerInterface $logger
    ) {
        $this->pool = $this->poolFactory->get('bacc_connector', function () {
            return $this->createConnector();
        }, [
            'max_connections' => 200,
            'min_connections' => 10,
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
        ]);
    }

    /**
     * 获取连接池中的连接器
     */
    public function getConnector(): BaccConnector
    {
        $connection = $this->pool->get();

        try {
            return $connection->getConnection();
        } finally {
            $connection->release();
        }
    }

    /**
     * 创建新的连接器实例
     */
    private function createConnector(): BaccConnector
    {
        return new BaccConnector();
    }

    /**
     * 获取池统计信息
     */
    public function getPoolStats(): array
    {
        return [
            'connections_in_channel' => $this->pool->getConnectionsInChannel(),
            'current_connections' => $this->pool->getCurrentConnections(),
        ];
    }
}
