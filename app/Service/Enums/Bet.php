<?php

namespace App\Service\Enums;

use App\Service\LotteryResult;

enum Bet: string
{
    case BANKER = 'BANKER';
    case PLAYER = 'PLAYER';
    case TIE = 'TIE';

    public function getOpposite(): string
    {
        return match ($this) {
            self::BANKER => LotteryResult::PLAYER,
            self::PLAYER => LotteryResult::BANKER,
            self::TIE => LotteryResult::TIE, 
        };
    }
    
}