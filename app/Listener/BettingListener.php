<?php

declare(strict_types=1);

namespace App\Listener;

use App\Event\BettingEvent;
use Hyperf\Event\Annotation\Listener;
use Psr\Container\ContainerInterface;
use Hyperf\Event\Contract\ListenerInterface;
use App\Repository\Baccarat\BaccaratTerraceRepository;
use App\Repository\Baccarat\BaccaratTerraceDeckRepository;
use App\Repository\Baccarat\BaccaratLotteryLogRepository;
use App\Model\BaccaratLotteryLog;
use App\Model\BaccaratBettingLog;
use App\Service\Platform\Bacc\Contract\PlatformClientInterface;
use App\Service\Output\Output;
use App\Model\BaccaratTerrace;
use App\Model\BaccaratTerraceDeck;
use App\Service\Platform\Bacc\BaccResponse as Response;
use App\Service\LotteryResult;

#[Listener]
class BettingListener implements ListenerInterface
{

    public function __construct(
        protected ContainerInterface         $container,
        protected BaccaratTerraceRepository $baccaratTerraceRepository,
        protected BaccaratTerraceDeckRepository $baccaratTerraceDeckRepository,
        protected BaccaratLotteryLogRepository $baccaratLotteryLogRepository,
        protected PlatformClientInterface $platformClient,
        protected Output $output,
    )
    {
    }

    public function listen(): array
    {
        return [
            BettingEvent::class,
        ];
    }

    public function process(object $event): void
    {
        /** @var BettingEvent $event */
        $lotteryResult = $event->lotteryResult;

        if (!$lotteryResult->isBetting() || !$lotteryResult->getDeckNumber()) {
            return;
        }


        // 根据 code 获取台号且不存在就创建
        $baccaratTerrace = $this->baccaratTerraceRepository->firstOrCreate(['code' => $lotteryResult->terrace],['title' => $lotteryResult->getTerrainTableName()]);

        // 根据台号获取今日台靴不存在则创建
        $baccaratTerraceDeck = $this->baccaratTerraceDeckRepository->firstOrCreateWithToday(
            $baccaratTerrace->id,
            $lotteryResult->getDeckNumber()
        );

        // 根据期号获取开奖日志不存在则创建
        $this->baccaratLotteryLogRepository->firstOrCreate(
            $baccaratTerraceDeck->id,
            (int)$lotteryResult->issue
        );


          // 获取牌面所有开奖结果
        if (!$transformationResult = $baccaratTerraceDeck->lotterySequence) {
            return;
        }

         // 计算结果和投注操作
         $baccaratLotterySequenceString = str_replace(['B', 'P', 'T'], ['1', '0', ''], $transformationResult);
         $baccaratLotterySequence = array_map('intval', str_split($baccaratLotterySequenceString));
         
         if (count($baccaratLotterySequence) < 7) {
             return;
         }

         //判断是否有为 null 的开奖,有则说明此局开奖数据不完整
         $lotteryLog = BaccaratLotteryLog::query()
         ->whereNull('result')
         ->whereNotIn('issue',[$lotteryResult->issue])
         ->where('terrace_deck_id',$baccaratTerraceDeck->id)
         ->exists();

        if($lotteryLog){
            $this->output->error(
                sprintf(
                    "terrace:%s terrace_id:%d deck_id:%d issue:%s result is null",
                    $lotteryResult->getTerrainTableName(),
                    $baccaratTerrace->id,
                    $baccaratTerraceDeck->id,
                    $lotteryResult->issue,
                )
            );
            return;
        }

        $response = $this->platformClient->calculate($baccaratLotterySequence);
        if($response->getConfidence() < 70 || !$response->getBet()){
            return;
        }

        $this->logCalculationResult($lotteryResult,$baccaratTerrace,$baccaratTerraceDeck,$baccaratLotterySequenceString,$response);

         // 检查是否存在模拟投注记录
         $baccaratSimulatedBettingLog = BaccaratBettingLog::where('issue',$lotteryResult->issue)->exists();
         if($baccaratSimulatedBettingLog){
             return;
         }
 
 
         // 创建投注记录
         BaccaratBettingLog::create([
             'terrace_deck_id' => $baccaratTerraceDeck->id,
             'issue'           => $lotteryResult->issue,
             'betting_type'   => $response->getBet()?->getOpposite(),
             'confidence'     => $response->getConfidence(),
             'response'        => $response->toArray(),
         ]);


         $this->logBettingSuccess($lotteryResult,$baccaratTerrace,$baccaratTerraceDeck,$response);
    }

    private function logBettingSuccess(LotteryResult $result,BaccaratTerrace $terrace,BaccaratTerraceDeck $deck,Response $response): void
    {
        $this->output->error(sprintf(
            "enter room terrace_id:%d deck_id:%d deck:%s issue:%s confidence:%d betting_value:%s",
            $terrace->id,
            $deck->id,
            $result->getTerrainTableName(),
            $result->issue,
            $response->getConfidence(),
            $response->getBet()?->getOpposite()
        ));
    }

    private function logCalculationResult(LotteryResult $result,BaccaratTerrace $terrace,BaccaratTerraceDeck $deck, string $baccaratLotterySequenceString,Response $response): void
    {
        $this->output->warn(string: sprintf(
            "deck:%s terrace_id:%d deck_id:%d issue:%s convert %s message:%s",
            $result->getTerrainTableName(),
            $terrace->id,
            $deck->id,
            $result->issue,
            $baccaratLotterySequenceString,
            $response->toJson()
        ));
    }
}
