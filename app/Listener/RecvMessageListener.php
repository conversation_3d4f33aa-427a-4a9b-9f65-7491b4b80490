<?php

declare(strict_types=1);

namespace App\Listener;

use App\Event\BettingEvent;
use App\Event\RecvMessageEvent;
use App\Event\WaitingEvent;
use App\Service\LoggerFactory;
use App\Service\LotteryResult;
use App\Service\Output\Output;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Redis\RedisFactory;
use Hyperf\Redis\RedisProxy;
use Lysice\HyperfRedisLock\LockTimeoutException;
use Lysice\HyperfRedisLock\RedisLock;
use Psr\Container\ContainerInterface;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;

#[Listener]
class RecvMessageListener implements ListenerInterface
{
    /**
     * @var LoggerInterface
     */
    protected LoggerInterface $logger;
    protected RedisProxy $redisProxy;

    protected ?EventDispatcherInterface $eventDispatcher = null;

    public function __construct(
        protected ContainerInterface         $container,
        protected Output         $output,
        protected LoggerFactory $loggerFactory
    )
    {
        $this->redisProxy = make(RedisFactory::class)->get('default');
        
    }

    public function listen(): array
    {
        return [
            RecvMessageEvent::class,
        ];
    }

    public function process(object $event): void
    {
        /**
         * @var RecvMessageEvent $event
         */

        $lotteryResult = $event->lotteryResult;

        $this->logger = $this->loggerFactory->create($lotteryResult->terrace, 'baccarat');

        $this->logger->debug($lotteryResult);

        if ($lotteryResult->needDrawCard() && $lotteryResult->isWaiting()) {
            $this->output->info($lotteryResult);
        }


        $this->eventDispatcher = $this->container->get(EventDispatcherInterface::class);

        if ($lotteryResult->isWaiting() || $lotteryResult->isBetting() || !$lotteryResult->needDrawCard()){

            //使用时间戳避免锁值重复
            $redisLock = new RedisLock($this->redisProxy, "recv_message_lock_{$lotteryResult->status}_{$lotteryResult->issue}", 1);

            try {

                $redisLock->block(10, fn() => $this->dispatch($lotteryResult));

            } catch (LockTimeoutException $e) {

                $this->output->info("recv_message_lock_{$lotteryResult->status}_{$lotteryResult->issue} lock timeout");
            }
        }
    }

    public function dispatch(LotteryResult $lotteryResult): void
    {
        match (true){
            $lotteryResult->isWaiting() || !$lotteryResult->needDrawCard()=> $this->eventDispatcher->dispatch(new WaitingEvent($lotteryResult)),
            $lotteryResult->isBetting() => $this->eventDispatcher->dispatch(new BettingEvent($lotteryResult)),
            default => null,
        };
    }
}
