<?php

declare(strict_types=1);

namespace App\Listener;

use App\Event\WaitingEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Psr\Container\ContainerInterface;
use App\Repository\Baccarat\BaccaratLotteryLogRepository;
use App\Model\BaccaratBettingLog;
#[Listener]
class WaitingListener implements ListenerInterface
{
    public function __construct(
        protected ContainerInterface         $container,
        protected BaccaratLotteryLogRepository $baccaratLotteryLogRepository,
    )
    {
    }

    public function listen(): array
    {
        return [
            WaitingEvent::class,
        ];
    }

    public function process(object $event): void
    {
        /**
         * @var WaitingEvent $event
         */
        $lotteryResult = $event->lotteryResult;

        $this->baccaratLotteryLogRepository->updateLotteryLog($lotteryResult);


        BaccaratBettingLog::where('issue',$lotteryResult->issue)
            ->whereNotNull('betting_type')
            ->whereNull('betting_result')
            ->get()
            ->each(fn (BaccaratBettingLog $baccaratBettingLog) => $baccaratBettingLog->update(['betting_result' => $lotteryResult->checkLotteryResults($baccaratBettingLog->betting_type->value)]));
    }
}
