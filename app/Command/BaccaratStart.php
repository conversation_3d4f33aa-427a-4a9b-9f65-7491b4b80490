<?php

declare(strict_types=1);

namespace App\Command;

use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Psr\Container\ContainerInterface;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Engine\Channel;
use Hyperf\Redis\RedisFactory;
use Swoole\Runtime;
use App\Service\LoggerFactory;
use App\Service\Output\Output;
use App\Service\Websocket\ConnectionPool;
use App\Service\Websocket\WebsocketClientFactory;
use App\Service\Websocket\WebSocketManageService;
use Lysice\HyperfRedisLock\RedisLock;
use Psr\EventDispatcher\EventDispatcherInterface;
#[Command]
class BaccaratStart extends HyperfCommand
{
    public function __construct(
        protected ContainerInterface $container,
        protected ConfigInterface $config,
        protected RedisFactory $redisFactory,
        )
    {
        parent::__construct('baccarat:websocket:start');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('启动百家乐客户端服务');
    }

    public function handle()
    {
        if (!$this->config->get('baccarat.websocket')){
            throw new \Exception('baccarat.websocket config is not exist');
        }

        // 此行代码后，文件操作，sleep，Mysqli，PDO，streams等都变成异步IO，见'一键协程化'章节。
        if(Runtime::enableCoroutine() === false){
            $this->error("一键协程化失败");
            return;
        }

        $channel = new Channel(5000);

        $websocketClientFactory = new websocketClientFactory(
            channel: $channel,
            host: $this->config->get('baccarat.websocket.host'),
            token: $this->config->get('baccarat.websocket.token'),
            connectionTimeout: $this->config->get('baccarat.websocket.connectionTimeout')
        );

        $ConnectionPool = new ConnectionPool(
            container: $this->container,
            websocketClientFactory: $websocketClientFactory,
            output: make(Output::class),
            config: $this->config->get('baccarat.websocket.connectionPool'),
        );

        $redis = make(RedisFactory::class)->get('default');

        //使用时间戳避免锁值重复
        $redisLock = new RedisLock($redis, "baccarat:websocket:lock:".time(), 600);

        $reconnectLock = new RedisLock($redis, "baccarat:websocket:reconnect:lock:".time(), 60);

        $WebSocketManageService = new WebSocketManageService(
            websocketClientFactory: $websocketClientFactory,
            connectionPool:$ConnectionPool,
            channel: $channel,
            output: make(Output::class),
            dispatcher: $this->container->get(EventDispatcherInterface::class),
            loggerFactory: $this->container->get(LoggerFactory::class),
            redisLock: $redisLock,
            reconnectLock: $reconnectLock,
            concurrentSize: 10,
            websocketSize: 3
        );

        $WebSocketManageService->run();
    }
}
