<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\Enums\Baccarat\LotteryResult;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\Database\Model\Relations\HasOneThrough;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 百家乐开奖日志模型
 * 
 * @property int $id 主键ID
 * @property int $terrace_deck_id 牌靴ID
 * @property int $issue 期号（局号）
 * @property string|null $result 开奖结果：B=庄赢，P=闲赢，T=和局
 * @property LotteryResult|null $transformation_result 转换后开奖结果
 * @property string|null $banker_cards 庄家牌型
 * @property string|null $player_cards 闲家牌型
 * @property int|null $banker_points 庄家点数
 * @property int|null $player_points 闲家点数
 * @property array|null $raw_data 原始开奖数据
 * @property \Carbon\Carbon|null $lottery_time 开奖时间
 * @property string|null $remark 备注说明
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * 
 * @property-read BaccaratTerraceDeck $terraceDeck 所属牌靴
 * @property-read BaccaratTerrace $terrace 所属台桌
 * @property-read Collection<BaccaratBettingLog> $bettingLogs 投注记录
 * @property-read string $result_text 开奖结果文本
 * @property-read bool $is_banker_win 是否庄赢
 * @property-read bool $is_player_win 是否闲赢
 * @property-read bool $is_tie 是否和局
 * @property-read int $points_diff 点数差
 */
class BaccaratLotteryLog extends MineModel
{
    /**
     * 表名
     */
    protected ?string $table = 'baccarat_lottery_log';

    /**
     * 主键字段
     */
    protected string $primaryKey = 'id';

    /**
     * 主键类型
     */
    protected string $keyType = 'int';

    /**
     * 是否自增主键
     */
    public bool $incrementing = true;

    /**
     * 是否使用时间戳
     */
    public bool $timestamps = true;

    /**
     * 可批量赋值的属性
     */
    protected array $fillable = [
        'terrace_deck_id',
        'issue',
        'result',
        'transformation_result',
        'banker_cards',
        'player_cards',
        'banker_points',
        'player_points',
        'raw_data',
        'remark',
    ];

    /**
     * 类型转换
     */
    protected array $casts = [
        'id' => 'integer',
        'terrace_deck_id' => 'integer',
        'issue' => 'integer',
        'transformation_result' => LotteryResult::class,
        'banker_points' => 'integer',
        'player_points' => 'integer',
        'raw_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     */
    protected array $hidden = [];

    /**
     * 获取开奖结果文本（安全版本）
     */
    public function getResultTextAttribute(): string
    {
        return $this->transformation_result?->getText() ?? '未开奖';
    }

    /**
     * 是否庄赢（安全版本）
     */
    public function getIsBankerWinAttribute(): bool
    {
        return $this->transformation_result?->isBankerWin() ?? false;
    }

    /**
     * 是否闲赢（安全版本）
     */
    public function getIsPlayerWinAttribute(): bool
    {
        return $this->transformation_result?->isPlayerWin() ?? false;
    }

    /**
     * 是否和局（安全版本）
     */
    public function getIsTieAttribute(): bool
    {
        return $this->transformation_result?->isTie() ?? false;
    }



    /**
     * 获取点数差
     */
    public function getPointsDiffAttribute(): int
    {
        if ($this->banker_points === null || $this->player_points === null) {
            return 0;
        }

        return abs($this->banker_points - $this->player_points);
    }

    /**
     * 多对一关联：开奖日志属于一个牌靴
     */
    public function terraceDeck(): BelongsTo
    {
        return $this->belongsTo(BaccaratTerraceDeck::class, 'terrace_deck_id', 'id');
    }

    /**
     * 通过牌靴关联到台桌
     */
    public function terrace(): HasOneThrough
    {
        return $this->hasOneThrough(
            BaccaratTerrace::class,
            BaccaratTerraceDeck::class,
            'id',           // 中间表外键
            'id',           // 目标表主键
            'terrace_deck_id', // 本表外键
            'terrace_id'    // 中间表关联目标表的外键
        );
    }

    /**
     * 一对多关联：开奖记录拥有多个投注记录
     */
    public function bettingLogs(): HasMany
    {
        return $this->hasMany(BaccaratBettingLog::class, 'lottery_log_id', 'id');
    }

    /**
     * 作用域：庄赢
     */
    public function scopeBankerWin($query)
    {
        return $query->where('transformation_result', LotteryResult::BANKER);
    }

    /**
     * 作用域：闲赢
     */
    public function scopePlayerWin($query)
    {
        return $query->where('transformation_result', LotteryResult::PLAYER);
    }

    /**
     * 作用域：和局
     */
    public function scopeTie($query)
    {
        return $query->where('transformation_result', LotteryResult::TIE);
    }

    /**
     * 作用域：获胜结果（排除和局）
     */
    public function scopeWins($query)
    {
        return $query->whereIn('transformation_result', [LotteryResult::BANKER, LotteryResult::PLAYER]);
    }

    /**
     * 作用域：按期号排序
     */
    public function scopeOrderedByIssue($query)
    {
        return $query->orderBy('issue', 'asc');
    }

    /**
     * 作用域：按开奖时间排序
     */
    public function scopeOrderedByLotteryTime($query)
    {
        return $query->orderBy('lottery_time', 'desc');
    }

    /**
     * 作用域：今日开奖
     */
    public function scopeToday($query)
    {
        return $query->whereDate('lottery_time', Carbon::now()->toDateString());
    }

    /**
     * 作用域：指定日期范围
     */
    public function scopeDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('lottery_time', [$startDate, $endDate]);
    }

    /**
     * 格式化牌型显示
     */
    public function formatCards(string $cards): string
    {
        if (empty($cards)) {
            return '';
        }

        // 假设牌型格式为：H5,D7,S3 (花色+点数)
        $cardArray = explode(',', $cards);
        $formattedCards = [];

        foreach ($cardArray as $card) {
            if (strlen($card) >= 2) {
                $suit = substr($card, 0, 1);
                $value = substr($card, 1);

                $suitSymbol = match ($suit) {
                    'H' => '♥',  // 红桃
                    'D' => '♦',  // 方块
                    'C' => '♣',  // 梅花
                    'S' => '♠',  // 黑桃
                    default => $suit
                };

                $formattedCards[] = $suitSymbol . $value;
            }
        }

        return implode(' ', $formattedCards);
    }

    /**
     * 获取格式化的庄家牌型
     */
    public function getFormattedBankerCardsAttribute(): string
    {
        return $this->formatCards($this->banker_cards ?? '');
    }

    /**
     * 获取格式化的闲家牌型
     */
    public function getFormattedPlayerCardsAttribute(): string
    {
        return $this->formatCards($this->player_cards ?? '');
    }

    /**
     * 计算百家乐点数
     */
    public static function calculatePoints(string $cards): int
    {
        if (empty($cards)) {
            return 0;
        }

        $cardArray = explode(',', $cards);
        $totalPoints = 0;

        foreach ($cardArray as $card) {
            if (strlen($card) >= 2) {
                $value = substr($card, 1);

                // 转换牌面值为点数
                $points = match ($value) {
                    'A' => 1,
                    'J', 'Q', 'K' => 0,
                    default => intval($value)
                };

                $totalPoints += $points;
            }
        }

        // 百家乐只取个位数
        return $totalPoints % 10;
    }

    /**
     * 自动计算点数和判断结果
     */
    public function autoCalculate(): void
    {
        // 自动计算庄家点数
        if ($this->banker_cards && $this->banker_points === null) {
            $this->banker_points = self::calculatePoints($this->banker_cards);
        }

        // 自动计算闲家点数
        if ($this->player_cards && $this->player_points === null) {
            $this->player_points = self::calculatePoints($this->player_cards);
        }

        // 自动判断开奖结果
        if ($this->banker_points !== null && $this->player_points !== null && $this->transformation_result === null) {
            if ($this->banker_points > $this->player_points) {
                $this->transformation_result = LotteryResult::BANKER;
            } elseif ($this->player_points > $this->banker_points) {
                $this->transformation_result = LotteryResult::PLAYER;
            } else {
                $this->transformation_result = LotteryResult::TIE;
            }
        }
    }

    /**
     * 安全设置开奖结果
     */
    public function setTransformationResult(?LotteryResult $result): void
    {
        $this->transformation_result = $result;
    }

    /**
     * 检查是否已开奖
     */
    public function hasResult(): bool
    {
        return $this->transformation_result !== null;
    }

    /**
     * 获取开奖统计信息
     */
    public static function getStatistics(int $terraceDeckId = null, string $startDate = null, string $endDate = null): array
    {
        $query = self::query();

        if ($terraceDeckId) {
            $query->where('terrace_deck_id', $terraceDeckId);
        }

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        $total = $query->count();
        $bankerWins = $query->clone()->bankerWin()->count();
        $playerWins = $query->clone()->playerWin()->count();
        $ties = $query->clone()->tie()->count();

        return [
            'total' => $total,
            'banker_wins' => $bankerWins,
            'player_wins' => $playerWins,
            'ties' => $ties,
            'banker_rate' => $total > 0 ? round($bankerWins / $total * 100, 2) : 0,
            'player_rate' => $total > 0 ? round($playerWins / $total * 100, 2) : 0,
            'tie_rate' => $total > 0 ? round($ties / $total * 100, 2) : 0,
            'win_rate' => $total > 0 ? round(($bankerWins + $playerWins) / $total * 100, 2) : 0,
        ];
    }

    /**
     * 获取结果分布统计
     */
    public static function getResultDistribution(int $terraceDeckId = null): array
    {
        $query = self::query();

        if ($terraceDeckId) {
            $query->where('terrace_deck_id', $terraceDeckId);
        }

        $results = [];
        foreach (LotteryResult::cases() as $result) {
            $count = $query->clone()->where('transformation_result', $result)->count();
            $results[$result->value] = [
                'value' => $result->value,
                'text' => $result->getText(),
                'count' => $count,
                'color' => $result->getColor(),
                'icon' => $result->getIcon(),
                'odds' => $result->getOdds(),
            ];
        }

        return $results;
    }
}
