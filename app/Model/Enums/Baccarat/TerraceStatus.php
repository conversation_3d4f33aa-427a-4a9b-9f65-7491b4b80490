<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\Baccarat;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

/**
 * 百家乐台桌状态枚举
 */
#[Constants]
enum TerraceStatus: int
{
    use EnumConstantsTrait;

    #[Message('baccarat.terrace.status.disabled')]
    case DISABLED = 0;

    #[Message('baccarat.terrace.status.enabled')]
    case ENABLED = 1;

    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this === self::ENABLED;
    }

    /**
     * 检查是否禁用
     */
    public function isDisabled(): bool
    {
        return $this === self::DISABLED;
    }

    /**
     * 获取状态文本
     */
    public function getText(): string
    {
        return match ($this) {
            self::ENABLED => '启用',
            self::DISABLED => '禁用',
        };
    }

    /**
     * 获取状态样式类
     */
    public function getStyleClass(): string
    {
        return match ($this) {
            self::ENABLED => 'success',
            self::DISABLED => 'danger',
        };
    }

    /**
     * 获取所有状态选项
     */
    public static function getOptions(): array
    {
        return [
            self::ENABLED->value => self::ENABLED->getText(),
            self::DISABLED->value => self::DISABLED->getText(),
        ];
    }
}
