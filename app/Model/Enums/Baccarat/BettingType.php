<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\Baccarat;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

/**
 * 百家乐投注类型枚举
 */
#[Constants]
enum BettingType: string
{
    use EnumConstantsTrait;

    #[Message('baccarat.betting.type.banker')]
    case BANKER = 'B';

    #[Message('baccarat.betting.type.player')]
    case PLAYER = 'P';

    #[Message('baccarat.betting.type.tie')]
    case TIE = 'T';

    #[Message('baccarat.betting.type.banker_pair')]
    case BANKER_PAIR = 'BP';

    #[Message('baccarat.betting.type.player_pair')]
    case PLAYER_PAIR = 'PP';

    #[Message('baccarat.betting.type.perfect_pair')]
    case PERFECT_PAIR = 'PF';

    #[Message('baccarat.betting.type.any_pair')]
    case ANY_PAIR = 'AP';

    #[Message('baccarat.betting.type.super_six')]
    case SUPER_SIX = 'S6';

    /**
     * 检查是否主要投注类型（庄、闲、和）
     */
    public function isMainBet(): bool
    {
        return in_array($this, [self::BANKER, self::PLAYER, self::TIE]);
    }

    /**
     * 检查是否对子投注
     */
    public function isPairBet(): bool
    {
        return in_array($this, [self::BANKER_PAIR, self::PLAYER_PAIR, self::PERFECT_PAIR, self::ANY_PAIR]);
    }

    /**
     * 检查是否边注
     */
    public function isSideBet(): bool
    {
        return !$this->isMainBet();
    }

    /**
     * 获取投注类型文本
     */
    public function getText(): string
    {
        return match ($this) {
            self::BANKER => '庄赢',
            self::PLAYER => '闲赢',
            self::TIE => '和局',
            self::BANKER_PAIR => '庄对',
            self::PLAYER_PAIR => '闲对',
            self::PERFECT_PAIR => '完美对子',
            self::ANY_PAIR => '任意对子',
            self::SUPER_SIX => '超级六',
        };
    }

    /**
     * 获取投注类型英文名称
     */
    public function getEnglishName(): string
    {
        return match ($this) {
            self::BANKER => 'Banker',
            self::PLAYER => 'Player',
            self::TIE => 'Tie',
            self::BANKER_PAIR => 'Banker Pair',
            self::PLAYER_PAIR => 'Player Pair',
            self::PERFECT_PAIR => 'Perfect Pair',
            self::ANY_PAIR => 'Any Pair',
            self::SUPER_SIX => 'Super Six',
        };
    }



    /**
     * 获取投注颜色
     */
    public function getColor(): string
    {
        return match ($this) {
            self::BANKER => '#ff4d4f',      // 红色
            self::PLAYER => '#1890ff',      // 蓝色
            self::TIE => '#52c41a',         // 绿色
            self::BANKER_PAIR => '#ff7875', // 浅红色
            self::PLAYER_PAIR => '#69c0ff', // 浅蓝色
            self::PERFECT_PAIR => '#b37feb', // 紫色
            self::ANY_PAIR => '#ffc069',    // 橙色
            self::SUPER_SIX => '#f759ab',   // 粉色
        };
    }

    /**
     * 获取投注图标
     */
    public function getIcon(): string
    {
        return match ($this) {
            self::BANKER => '🔴',
            self::PLAYER => '🔵',
            self::TIE => '🟢',
            self::BANKER_PAIR => '🟥',
            self::PLAYER_PAIR => '🟦',
            self::PERFECT_PAIR => '🟪',
            self::ANY_PAIR => '🟧',
            self::SUPER_SIX => '🟣',
        };
    }

    /**
     * 获取投注风险等级
     */
    public function getRiskLevel(): string
    {
        return match ($this) {
            self::BANKER, self::PLAYER => 'low',      // 低风险
            self::TIE => 'medium',                    // 中等风险
            self::BANKER_PAIR, self::PLAYER_PAIR => 'high', // 高风险
            self::PERFECT_PAIR, self::ANY_PAIR, self::SUPER_SIX => 'very_high', // 极高风险
        };
    }

    /**
     * 获取所有投注类型选项
     */
    public static function getOptions(): array
    {
        return [
            self::BANKER->value => self::BANKER->getText(),
            self::PLAYER->value => self::PLAYER->getText(),
            self::TIE->value => self::TIE->getText(),
            self::BANKER_PAIR->value => self::BANKER_PAIR->getText(),
            self::PLAYER_PAIR->value => self::PLAYER_PAIR->getText(),
            self::PERFECT_PAIR->value => self::PERFECT_PAIR->getText(),
            self::ANY_PAIR->value => self::ANY_PAIR->getText(),
            self::SUPER_SIX->value => self::SUPER_SIX->getText(),
        ];
    }

    /**
     * 获取主要投注选项
     */
    public static function getMainBetOptions(): array
    {
        return [
            self::BANKER->value => self::BANKER->getText(),
            self::PLAYER->value => self::PLAYER->getText(),
            self::TIE->value => self::TIE->getText(),
        ];
    }

    /**
     * 获取边注选项
     */
    public static function getSideBetOptions(): array
    {
        return [
            self::BANKER_PAIR->value => self::BANKER_PAIR->getText(),
            self::PLAYER_PAIR->value => self::PLAYER_PAIR->getText(),
            self::PERFECT_PAIR->value => self::PERFECT_PAIR->getText(),
            self::ANY_PAIR->value => self::ANY_PAIR->getText(),
            self::SUPER_SIX->value => self::SUPER_SIX->getText(),
        ];
    }

    /**
     * 从字符串创建枚举实例
     */
    public static function fromString(string $value): ?self
    {
        return match (strtoupper($value)) {
            'B', 'BANKER', '庄', '庄赢' => self::BANKER,
            'P', 'PLAYER', '闲', '闲赢' => self::PLAYER,
            'T', 'TIE', '和', '和局' => self::TIE,
            'BP', 'BANKER_PAIR', '庄对' => self::BANKER_PAIR,
            'PP', 'PLAYER_PAIR', '闲对' => self::PLAYER_PAIR,
            'PF', 'PERFECT_PAIR', '完美对子' => self::PERFECT_PAIR,
            'AP', 'ANY_PAIR', '任意对子' => self::ANY_PAIR,
            'S6', 'SUPER_SIX', '超级六' => self::SUPER_SIX,
            default => null,
        };
    }
}
