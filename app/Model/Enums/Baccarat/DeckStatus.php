<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\Baccarat;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

/**
 * 百家乐牌靴状态枚举
 */
#[Constants]
enum DeckStatus: int
{
    use EnumConstantsTrait;

    #[Message('baccarat.deck.status.ended')]
    case ENDED = 0;

    #[Message('baccarat.deck.status.playing')]
    case PLAYING = 1;

    #[Message('baccarat.deck.status.paused')]
    case PAUSED = 2;

    /**
     * 检查是否进行中
     */
    public function isPlaying(): bool
    {
        return $this === self::PLAYING;
    }

    /**
     * 检查是否已结束
     */
    public function isEnded(): bool
    {
        return $this === self::ENDED;
    }

    /**
     * 检查是否暂停
     */
    public function isPaused(): bool
    {
        return $this === self::PAUSED;
    }

    /**
     * 检查是否活跃状态（进行中或暂停）
     */
    public function isActive(): bool
    {
        return $this === self::PLAYING || $this === self::PAUSED;
    }

    /**
     * 获取状态文本
     */
    public function getText(): string
    {
        return match ($this) {
            self::PLAYING => '进行中',
            self::ENDED => '已结束',
            self::PAUSED => '暂停',
        };
    }

    /**
     * 获取状态样式类
     */
    public function getStyleClass(): string
    {
        return match ($this) {
            self::PLAYING => 'success',
            self::ENDED => 'info',
            self::PAUSED => 'warning',
        };
    }

    /**
     * 获取可切换的状态
     */
    public function getNextStatuses(): array
    {
        return match ($this) {
            self::PLAYING => [self::PAUSED, self::ENDED],
            self::PAUSED => [self::PLAYING, self::ENDED],
            self::ENDED => [], // 已结束不能切换到其他状态
        };
    }

    /**
     * 获取所有状态选项
     */
    public static function getOptions(): array
    {
        return [
            self::PLAYING->value => self::PLAYING->getText(),
            self::PAUSED->value => self::PAUSED->getText(),
            self::ENDED->value => self::ENDED->getText(),
        ];
    }

    /**
     * 获取活跃状态选项
     */
    public static function getActiveOptions(): array
    {
        return [
            self::PLAYING->value => self::PLAYING->getText(),
            self::PAUSED->value => self::PAUSED->getText(),
        ];
    }
}
