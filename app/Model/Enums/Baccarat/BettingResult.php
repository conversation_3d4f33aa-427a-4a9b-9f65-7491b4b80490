<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\Baccarat;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

/**
 * 百家乐投注结果枚举
 */
#[Constants]
enum BettingResult: int
{
    use EnumConstantsTrait;

    #[Message('baccarat.betting.result.lose')]
    case LOSE = 0;

    #[Message('baccarat.betting.result.win')]
    case WIN = 1;

    #[Message('baccarat.betting.result.push')]
    case PUSH = 2;

    /**
     * 检查是否获胜
     */
    public function isWin(): bool
    {
        return $this === self::WIN;
    }

    /**
     * 检查是否失败
     */
    public function isLose(): bool
    {
        return $this === self::LOSE;
    }

    /**
     * 检查是否平局（退还）
     */
    public function isPush(): bool
    {
        return $this === self::PUSH;
    }

    /**
     * 获取结果文本
     */
    public function getText(): string
    {
        return match ($this) {
            self::WIN => '赢',
            self::LOSE => '输',
            self::PUSH => '和局退还',
        };
    }

    /**
     * 获取英文名称
     */
    public function getEnglishName(): string
    {
        return match ($this) {
            self::WIN => 'Win',
            self::LOSE => 'Lose',
            self::PUSH => 'Push',
        };
    }

    /**
     * 获取结果颜色
     */
    public function getColor(): string
    {
        return match ($this) {
            self::WIN => '#52c41a',   // 绿色（成功）
            self::LOSE => '#ff4d4f',  // 红色（失败）
            self::PUSH => '#faad14',  // 黄色（中性）
        };
    }

    /**
     * 获取结果图标
     */
    public function getIcon(): string
    {
        return match ($this) {
            self::WIN => '✅',
            self::LOSE => '❌',
            self::PUSH => '↩️',
        };
    }

    /**
     * 获取结果样式类
     */
    public function getStyleClass(): string
    {
        return match ($this) {
            self::WIN => 'success',
            self::LOSE => 'danger',
            self::PUSH => 'warning',
        };
    }

    /**
     * 获取结果状态描述
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::WIN => '投注成功，获得奖金',
            self::LOSE => '投注失败，损失本金',
            self::PUSH => '和局退还，退回本金',
        };
    }



    /**
     * 获取所有结果选项
     */
    public static function getOptions(): array
    {
        return [
            self::WIN->value => self::WIN->getText(),
            self::LOSE->value => self::LOSE->getText(),
            self::PUSH->value => self::PUSH->getText(),
        ];
    }

    /**
     * 获取有效结果选项（排除和局）
     */
    public static function getValidOptions(): array
    {
        return [
            self::WIN->value => self::WIN->getText(),
            self::LOSE->value => self::LOSE->getText(),
        ];
    }

    /**
     * 根据投注类型和开奖结果计算投注结果
     */
    public static function calculateResult(BettingType $bettingType, LotteryResult $lotteryResult): self
    {
        return match ($bettingType) {
            BettingType::BANKER => match ($lotteryResult) {
                LotteryResult::BANKER => self::WIN,
                LotteryResult::PLAYER => self::LOSE,
                LotteryResult::TIE => self::PUSH,
            },
            BettingType::PLAYER => match ($lotteryResult) {
                LotteryResult::PLAYER => self::WIN,
                LotteryResult::BANKER => self::LOSE,
                LotteryResult::TIE => self::PUSH,
            },
            BettingType::TIE => match ($lotteryResult) {
                LotteryResult::TIE => self::WIN,
                LotteryResult::BANKER, LotteryResult::PLAYER => self::LOSE,
            },
            // 对子投注需要根据具体牌型判断，这里默认返回输
            default => self::LOSE,
        };
    }



    /**
     * 从字符串创建枚举实例
     */
    public static function fromString(string $value): ?self
    {
        return match (strtolower($value)) {
            'win', 'won', '1', '赢', '中' => self::WIN,
            'lose', 'lost', '0', '输', '不中' => self::LOSE,
            'push', 'tie', '2', '和', '退还' => self::PUSH,
            default => null,
        };
    }
}
