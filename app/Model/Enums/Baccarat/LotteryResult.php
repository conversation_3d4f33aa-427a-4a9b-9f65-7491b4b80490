<?php

declare(strict_types=1);
/**
 * This file is part of MineAdmin.
 *
 * @link     https://www.mineadmin.com
 * @document https://doc.mineadmin.com
 * @contact  <EMAIL>
 * @license  https://github.com/mineadmin/MineAdmin/blob/master/LICENSE
 */

namespace App\Model\Enums\Baccarat;

use Hyperf\Constants\Annotation\Constants;
use Hyperf\Constants\Annotation\Message;
use Hyperf\Constants\EnumConstantsTrait;

/**
 * 百家乐开奖结果枚举
 */
#[Constants]
enum LotteryResult: string
{
    use EnumConstantsTrait;

    #[Message('baccarat.lottery.result.banker')]
    case BANKER = 'B';

    #[Message('baccarat.lottery.result.player')]
    case PLAYER = 'P';

    #[Message('baccarat.lottery.result.tie')]
    case TIE = 'T';

    /**
     * 检查是否庄赢
     */
    public function isBankerWin(): bool
    {
        return $this === self::BANKER;
    }

    /**
     * 检查是否闲赢
     */
    public function isPlayerWin(): bool
    {
        return $this === self::PLAYER;
    }

    /**
     * 检查是否和局
     */
    public function isTie(): bool
    {
        return $this === self::TIE;
    }

    /**
     * 获取结果文本
     */
    public function getText(): string
    {
        return match ($this) {
            self::BANKER => '庄赢',
            self::PLAYER => '闲赢',
            self::TIE => '和局',
        };
    }

    /**
     * 获取英文名称
     */
    public function getEnglishName(): string
    {
        return match ($this) {
            self::BANKER => 'Banker',
            self::PLAYER => 'Player',
            self::TIE => 'Tie',
        };
    }

    /**
     * 获取结果颜色
     */
    public function getColor(): string
    {
        return match ($this) {
            self::BANKER => '#ff4d4f', // 红色
            self::PLAYER => '#1890ff', // 蓝色
            self::TIE => '#52c41a',     // 绿色
        };
    }

    /**
     * 获取结果样式类
     */
    public function getStyleClass(): string
    {
        return match ($this) {
            self::BANKER => 'banker-win',
            self::PLAYER => 'player-win',
            self::TIE => 'tie-result',
        };
    }

    /**
     * 获取结果图标
     */
    public function getIcon(): string
    {
        return match ($this) {
            self::BANKER => '🔴', // 红圆
            self::PLAYER => '🔵', // 蓝圆
            self::TIE => '🟢',    // 绿圆
        };
    }

    /**
     * 计算赔率（标准百家乐赔率）
     */
    public function getOdds(): float
    {
        return match ($this) {
            self::BANKER => 0.95, // 庄赢1:0.95（扣除5%佣金）
            self::PLAYER => 1.0,  // 闲赢1:1
            self::TIE => 8.0,     // 和局1:8
        };
    }

    /**
     * 获取统计权重（用于概率计算）
     */
    public function getStatWeight(): int
    {
        return match ($this) {
            self::BANKER => 1,
            self::PLAYER => 1,
            self::TIE => 1,
        };
    }

    /**
     * 获取所有结果选项
     */
    public static function getOptions(): array
    {
        return [
            self::BANKER->value => self::BANKER->getText(),
            self::PLAYER->value => self::PLAYER->getText(),
            self::TIE->value => self::TIE->getText(),
        ];
    }

    /**
     * 获取获胜结果选项（排除和局）
     */
    public static function getWinOptions(): array
    {
        return [
            self::BANKER->value => self::BANKER->getText(),
            self::PLAYER->value => self::PLAYER->getText(),
        ];
    }

    /**
     * 从字符串创建枚举实例
     */
    public static function fromString(string $value): ?self
    {
        return match (strtoupper($value)) {
            'B', 'BANKER', '庄', '庄赢' => self::BANKER,
            'P', 'PLAYER', '闲', '闲赢' => self::PLAYER,
            'T', 'TIE', '和', '和局' => self::TIE,
            default => null,
        };
    }

    /**
     * 获取相对结果（获胜方对比）
     */
    public function getOpposite(): ?self
    {
        return match ($this) {
            self::BANKER => self::PLAYER,
            self::PLAYER => self::BANKER,
            self::TIE => null, // 和局没有对立面
        };
    }
}
