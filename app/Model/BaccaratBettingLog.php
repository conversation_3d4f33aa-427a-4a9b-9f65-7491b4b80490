<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\Enums\Baccarat\BettingResult;
use App\Model\Enums\Baccarat\BettingType;
use Carbon\Carbon;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\HasOneThrough;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 百家乐投注日志模型
 * 
 * @property int $id 主键ID
 * @property int $lottery_log_id 开奖记录ID
 * @property int $terrace_deck_id 牌靴ID
 * @property int $issue 期号（冗余字段，便于查询）
 * @property BettingType $betting_type 投注类型
 * @property BettingResult|null $betting_result 投注结果
 * @property float|null $confidence 可信度
 * @property array|null $response 投注结果数据
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * 
 * @property-read BaccaratLotteryLog $lotteryLog 关联的开奖记录
 * @property-read BaccaratTerraceDeck $terraceDeck 所属牌靴
 * @property-read BaccaratTerrace $terrace 所属台桌
 * @property-read bool $is_settled 是否已结算
 * @property-read string $betting_status 投注状态
 */
class BaccaratBettingLog extends MineModel
{
    /**
     * 表名
     */
    protected ?string $table = 'baccarat_betting_log';

    /**
     * 主键字段
     */
    protected string $primaryKey = 'id';

    /**
     * 主键类型
     */
    protected string $keyType = 'int';

    /**
     * 是否自增主键
     */
    public bool $incrementing = true;

    /**
     * 是否使用时间戳
     */
    public bool $timestamps = true;

    /**
     * 可批量赋值的属性
     */
    protected array $fillable = [
        'lottery_log_id',
        'terrace_deck_id',
        'issue',
        'betting_type',
        'betting_result',
        'confidence',
        'response',
    ];

    /**
     * 类型转换
     */
    protected array $casts = [
        'id' => 'integer',
        'lottery_log_id' => 'integer',
        'terrace_deck_id' => 'integer',
        'issue' => 'integer',
        'betting_type' => BettingType::class,
        'betting_result' => BettingResult::class,
        'confidence' => 'decimal:2',
        'response' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     */
    protected array $hidden = [];


    /**
     * 多对一关联：投注记录属于一个开奖记录
     */
    public function lotteryLog(): BelongsTo
    {
        return $this->belongsTo(BaccaratLotteryLog::class, 'lottery_log_id', 'id');
    }

    /**
     * 多对一关联：投注记录属于一个牌靴
     */
    public function terraceDeck(): BelongsTo
    {
        return $this->belongsTo(BaccaratTerraceDeck::class, 'terrace_deck_id', 'id');
    }

    /**
     * 通过牌靴关联到台桌
     */
    public function terrace(): HasOneThrough
    {
        return $this->hasOneThrough(
            BaccaratTerrace::class,
            BaccaratTerraceDeck::class,
            'id',              // 中间表外键
            'id',              // 目标表主键
            'terrace_deck_id', // 本表外键
            'terrace_id'       // 中间表关联目标表的外键
        );
    }

    /**
     * 作用域：已结算的投注
     */
    public function scopeSettled($query)
    {
        return $query->whereNotNull('betting_result');
    }

    /**
     * 作用域：未结算的投注
     */
    public function scopeUnsettled($query)
    {
        return $query->whereNull('betting_result');
    }

    /**
     * 作用域：获胜的投注
     */
    public function scopeWin($query)
    {
        return $query->where('betting_result', BettingResult::WIN->value);
    }

    /**
     * 作用域：失败的投注
     */
    public function scopeLose($query)
    {
        return $query->where('betting_result', BettingResult::LOSE->value);
    }

    /**
     * 作用域：和局退还的投注
     */
    public function scopePush($query)
    {
        return $query->where('betting_result', BettingResult::PUSH->value);
    }

    /**
     * 作用域：主要投注（庄、闲、和）
     */
    public function scopeMainBets($query)
    {
        return $query->whereIn('betting_type', [
            BettingType::BANKER->value,
            BettingType::PLAYER->value,
            BettingType::TIE->value,
        ]);
    }

    /**
     * 作用域：边注投注
     */
    public function scopeSideBets($query)
    {
        return $query->whereNotIn('betting_type', [
            BettingType::BANKER->value,
            BettingType::PLAYER->value,
            BettingType::TIE->value,
        ]);
    }

    /**
     * 作用域：今日投注
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::now()->toDateString());
    }

    /**
     * 作用域：指定日期范围
     */
    public function scopeDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：按创建时间排序
     */
    public function scopeOrderedByCreatedAt($query, string $direction = 'desc')
    {
        return $query->orderBy('created_at', $direction);
    }

    /**
     * 结算投注
     */
    public function settle(BettingResult $result): bool
    {
        return $this->update([
            'betting_result' => $result,
        ]);
    }

    /**
     * 自动结算投注（基于开奖结果）
     */
    public function autoSettle(): bool
    {
        if ($this->is_settled || !$this->lotteryLog || !$this->lotteryLog->hasResult()) {
            return false;
        }

        $result = BettingResult::calculateResult($this->betting_type, $this->lotteryLog->transformation_result);

        return $this->settle($result);
    }


    /**
     * 获取投注统计信息
     */
    public static function getStatistics(string $startDate = null, string $endDate = null): array
    {
        $query = self::query()->settled();

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        $total = $query->count();
        $wins = $query->clone()->win()->count();
        $loses = $query->clone()->lose()->count();
        $pushes = $query->clone()->push()->count();

        return [
            'total_bets' => $total,
            'wins' => $wins,
            'loses' => $loses,
            'pushes' => $pushes,
            'win_rate' => $total > 0 ? round($wins / $total * 100, 2) : 0,
            'lose_rate' => $total > 0 ? round($loses / $total * 100, 2) : 0,
            'push_rate' => $total > 0 ? round($pushes / $total * 100, 2) : 0,
        ];
    }
}
