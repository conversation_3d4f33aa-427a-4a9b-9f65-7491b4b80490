<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\Enums\Baccarat\DeckStatus;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 百家乐牌靴模型
 * 
 * @property int $id 主键ID
 * @property int $terrace_id 所属台桌ID
 * @property int $deck_number 牌靴编号
 * @property DeckStatus $status 状态：0=已结束，1=进行中，2=暂停
 * @property string|null $lottery_sequence 开奖序列（B=庄，P=闲，T=和）
 * @property int $total_rounds 总局数
 * @property string|null $remark 备注说明
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * 
 * @property-read BaccaratTerrace $terrace 所属台桌
 * @property-read string $lotterySequence 开奖序列
 * @property-read Collection<BaccaratLotteryLog> $lotteryLogs 开奖记录
 * @property-read Collection<BaccaratBettingLog> $bettingLogs 投注记录
 */
class BaccaratTerraceDeck extends MineModel
{
    /**
     * 表名
     */
    protected ?string $table = 'baccarat_terrace_deck';

    /**
     * 主键字段
     */
    protected string $primaryKey = 'id';

    /**
     * 主键类型
     */
    protected string $keyType = 'int';

    /**
     * 是否自增主键
     */
    public bool $incrementing = true;

    /**
     * 是否使用时间戳
     */
    public bool $timestamps = true;

    /**
     * 可批量赋值的属性
     */
    protected array $fillable = [
        'terrace_id',
        'deck_number',
        'status',
        'lottery_sequence',
        'total_rounds',
        'remark',
    ];

    /**
     * 类型转换
     */
    protected array $casts = [
        'id' => 'integer',
        'terrace_id' => 'integer',
        'deck_number' => 'integer',
        'status' => DeckStatus::class,
        'total_rounds' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     */
    protected array $hidden = [];


    public function getLotterySequenceAttribute(): string
    {
        if ($this->lotteryLogs->isNotEmpty()) {
            return $this->lotteryLogs->filter(
                fn(BaccaratLotteryLog $baccaratLotteryLog) =>
                $baccaratLotteryLog->transformation_result && !$baccaratLotteryLog->transformation_result->isTie()
            )
                ->pluck('transformation_result')
                ->implode('');
        }

        return '';
    }


    /**
     * 多对一关联：牌靴属于一个台桌
     */
    public function terrace(): BelongsTo
    {
        return $this->belongsTo(BaccaratTerrace::class, 'terrace_id', 'id');
    }

    /**
     * 一对多关联：牌靴拥有多个开奖记录
     */
    public function lotteryLogs(): HasMany
    {
        return $this->hasMany(BaccaratLotteryLog::class, 'terrace_deck_id', 'id');
    }

    /**
     * 一对多关联：牌靴拥有多个投注记录
     */
    public function bettingLogs(): HasMany
    {
        return $this->hasMany(BaccaratBettingLog::class, 'terrace_deck_id', 'id');
    }

    /**
     * 作用域：进行中的牌靴
     */
    public function scopePlaying($query)
    {
        return $query->where('status', DeckStatus::PLAYING->value);
    }

    /**
     * 作用域：已结束的牌靴
     */
    public function scopeEnded($query)
    {
        return $query->where('status', DeckStatus::ENDED->value);
    }

    /**
     * 作用域：暂停的牌靴
     */
    public function scopePaused($query)
    {
        return $query->where('status', DeckStatus::PAUSED->value);
    }

    /**
     * 作用域：活跃的牌靴（进行中或暂停）
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [DeckStatus::PLAYING->value, DeckStatus::PAUSED->value]);
    }

    /**
     * 作用域：按牌靴编号排序
     */
    public function scopeOrderedByDeckNumber($query)
    {
        return $query->orderBy('deck_number', 'asc');
    }

    /**
     * 作用域：按开始时间排序
     */
    public function scopeOrderedByStartTime($query)
    {
        return $query->orderBy('started_at', 'desc');
    }

    /**
     * 开始牌靴
     */
    public function start(): bool
    {
        if (!$this->status?->isPlaying()) {
            return $this->update(['status' => DeckStatus::PLAYING]);
        }
        return true;
    }

    /**
     * 暂停牌靴
     */
    public function pause(): bool
    {
        if ($this->status?->isPlaying()) {
            return $this->update(['status' => DeckStatus::PAUSED]);
        }
        return false;
    }

    /**
     * 结束牌靴
     */
    public function end(): bool
    {
        if (!$this->status?->isEnded()) {
            return $this->update(['status' => DeckStatus::ENDED]);
        }
        return true;
    }

    /**
     * 检查是否可以切换到指定状态
     */
    public function canChangeTo(DeckStatus $targetStatus): bool
    {
        if (!$this->status) {
            return false;
        }
        return in_array($targetStatus, $this->status->getNextStatuses());
    }

    /**
     * 切换到指定状态
     */
    public function changeTo(DeckStatus $targetStatus): bool
    {
        if (!$this->canChangeTo($targetStatus)) {
            return false;
        }
        return $this->update(['status' => $targetStatus]);
    }
}
