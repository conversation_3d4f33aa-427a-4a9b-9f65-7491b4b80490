<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\Enums\Baccarat\DeckStatus;
use App\Model\Enums\Baccarat\TerraceStatus;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\Database\Model\Relations\HasManyThrough;
use Hyperf\DbConnection\Model\Model as MineModel;

/**
 * 百家乐台桌模型
 * 
 * @property int $id 主键ID
 * @property string $code 台桌标识码
 * @property string $title 台桌名称
 * @property TerraceStatus $status 状态：0=禁用，1=启用
 * @property int $sort 排序权重

 * @property string|null $remark 备注说明
 * @property \Carbon\Carbon $created_at 创建时间
 * @property \Carbon\Carbon $updated_at 更新时间
 * 
 * @property-read Collection<BaccaratTerraceDeck> $decks 关联的牌靴
 * @property-read BaccaratTerraceDeck|null $currentDeck 当前进行中的牌靴
 * @property-read Collection<BaccaratLotteryLog> $lotteryLogs 所有开奖记录
 * @property-read Collection<BaccaratBettingLog> $bettingLogs 所有投注记录
 * @property-read int $total_decks 总牌靴数量
 * @property-read int $active_decks 进行中的牌靴数量
 */
class BaccaratTerrace extends MineModel
{
    /**
     * 表名
     */
    protected ?string $table = 'baccarat_terrace';

    /**
     * 主键字段
     */
    protected string $primaryKey = 'id';

    /**
     * 主键类型
     */
    protected string $keyType = 'int';

    /**
     * 是否自增主键
     */
    public bool $incrementing = true;

    /**
     * 是否使用时间戳
     */
    public bool $timestamps = true;

    /**
     * 可批量赋值的属性
     */
    protected array $fillable = [
        'code',
        'title',
        'status',
        'sort',
        'remark',
    ];

    /**
     * 类型转换
     */
    protected array $casts = [
        'id' => 'integer',
        'status' => TerraceStatus::class,
        'sort' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     */
    protected array $hidden = [];


    /**
     * 一对多关联：台桌拥有多个牌靴
     */
    public function decks(): HasMany
    {
        return $this->hasMany(BaccaratTerraceDeck::class, 'terrace_id', 'id');
    }

    /**
     * 获取当前进行中的牌靴
     */
    public function currentDeck(): HasMany
    {
        return $this->hasMany(BaccaratTerraceDeck::class, 'terrace_id', 'id')
            ->where('status', DeckStatus::PLAYING->value)
            ->limit(1);
    }

    /**
     * 通过牌靴关联到所有开奖记录
     */
    public function lotteryLogs(): HasManyThrough
    {
        return $this->hasManyThrough(
            BaccaratLotteryLog::class,
            BaccaratTerraceDeck::class,
            'terrace_id',     // 中间表外键
            'terrace_deck_id', // 目标表外键
            'id',             // 本表主键
            'id'              // 中间表主键
        );
    }

    /**
     * 通过牌靴关联到所有投注记录
     */
    public function bettingLogs(): HasManyThrough
    {
        return $this->hasManyThrough(
            BaccaratBettingLog::class,
            BaccaratTerraceDeck::class,
            'terrace_id',     // 中间表外键
            'terrace_deck_id', // 目标表外键
            'id',             // 本表主键
            'id'              // 中间表主键
        );
    }

    /**
     * 获取总牌靴数量
     */
    public function getTotalDecksAttribute(): int
    {
        return $this->decks()->count();
    }

    /**
     * 获取进行中的牌靴数量
     */
    public function getActiveDecksAttribute(): int
    {
        return $this->decks()->where('status', DeckStatus::PLAYING->value)->count();
    }

    /**
     * 作用域：仅启用的台桌
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', TerraceStatus::ENABLED->value);
    }

    /**
     * 作用域：仅禁用的台桌
     */
    public function scopeDisabled($query)
    {
        return $query->where('status', TerraceStatus::DISABLED->value);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'asc')->orderBy('id', 'asc');
    }
}
