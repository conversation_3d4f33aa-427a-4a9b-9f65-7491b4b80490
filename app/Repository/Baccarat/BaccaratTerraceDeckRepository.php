<?php
namespace App\Repository\Baccarat;

use App\Model\BaccaratTerraceDeck;
use App\Repository\IRepository;
use Carbon\Carbon;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;

final class BaccaratTerraceDeckRepository extends IRepository
{
    public function __construct(protected readonly BaccaratTerraceDeck $model) {}

    public function getBaccaratTerraceDeckWithToday(int $terraceId,string $deckNumber): BaccaratTerraceDeck|null
    {
        return $this->getModel()
            ->where('deck_number', $deckNumber)
            ->where('terrace_id', $terraceId)
            ->whereBetween('created_at', [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()])
            ->first();
    }

    public function firstOrCreateWithToday(int $terraceId,string $deckNumber): BaccaratTerraceDeck
    {
        $baccaratTerraceDeck = $this->getBaccaratTerraceDeckWithToday($terraceId,$deckNumber);

        if($baccaratTerraceDeck){
            return $baccaratTerraceDeck;
        }

        return $this->getModel()->create(['terrace_id'=>$terraceId, 'deck_number'=>$deckNumber]);
    }
}