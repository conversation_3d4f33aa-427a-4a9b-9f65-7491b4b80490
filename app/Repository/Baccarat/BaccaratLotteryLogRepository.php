<?php
namespace App\Repository\Baccarat;

use App\Model\BaccaratLotteryLog;
use App\Repository\IRepository;
use App\Service\LotteryResult;

final class BaccaratLotteryLogRepository extends IRepository
{
    public function __construct(protected readonly BaccaratLotteryLog $model) {}

    public function firstOrCreate(int $terraceDeckId, int $issue): BaccaratLotteryLog
    {
        return $this->model->firstOrCreate(['terrace_deck_id' => $terraceDeckId, 'issue' => $issue]);
    }

    public function updateLotteryLog(LotteryResult $lotteryResult):?BaccaratLotteryLog
    {
        $baccaratLotteryLog = $this->model->where('issue', $lotteryResult->issue)->first();

            if($baccaratLotteryLog && !$baccaratLotteryLog->transformation_result){

                $baccaratLotteryLog->update([
                    'result' => $lotteryResult->result,
                    'transformation_result' => $lotteryResult->getTransformationResult(),
                    'raw_data' => $lotteryResult->data,
                ]);
                return $baccaratLotteryLog;
            }
            return null;
    }
}