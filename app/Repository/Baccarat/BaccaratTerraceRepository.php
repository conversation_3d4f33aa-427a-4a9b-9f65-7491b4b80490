<?php

namespace App\Repository\Baccarat;

use App\Model\BaccaratTerrace;
use App\Repository\IRepository;

final class BaccaratTerraceRepository extends IRepository
{
    public function __construct(protected readonly BaccaratTerrace $model) {}


    public function firstOrCreate(array $attributes, array $data = []): BaccaratTerrace
    {
        return $this->model->firstOrCreate($attributes, $data);
    }
}