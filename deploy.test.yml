version: '3.7'
services:
  hyperf:
    image: hyperf/hyperf:8.1-alpine-v3.18-swoole
    environment:
      - "APP_PROJECT=MineAdmin"
      - "APP_ENV=test"
    ports:
      - 9501:9501
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 5
      update_config:
        parallelism: 2
        delay: 5s
        order: start-first
    configs:
      - source: hyperf_v1.0
        target: /opt/www/.env
configs:
  hyperf_v1.0:
    external: true
