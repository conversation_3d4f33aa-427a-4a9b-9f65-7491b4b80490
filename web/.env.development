# 页面标题
VITE_APP_TITLE = MineAdmin
# 端口
VITE_APP_PORT = 2888
# 应用根路径
VITE_APP_ROOT_BASE = /
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://127.0.0.1:9501
# 路由模式: history 和 hash 两种，默认hash，带#号那种
VITE_APP_ROUTE_MODE = hash

# 存储前缀
VITE_APP_STORAGE_PREFIX = mine_

# 是否开启代理
VITE_OPEN_PROXY = true
# 代理前缀标识
VITE_PROXY_PREFIX = /dev
# 是否开启vConsole （手机端调式可开启）
VITE_OPEN_vCONSOLE = false
# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = false
