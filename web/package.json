{"name": "mineadmin-ui", "type": "module", "version": "3.0.0", "engines": {"node": "^20.0.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "http-server ./dist -o", "svgo": "svgo -f src/assets/icons", "gen:icons": "esno scripts/gen.icons.mts", "plugin:publish": "esno scripts/plugin.publish.mts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc --noEmit", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "test:e2e": "playwright test --config=playwright.config.ts"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.4.4", "@mineadmin/echarts": "^1.0.5", "@mineadmin/form": "^1.0.51", "@mineadmin/pro-table": "^1.0.73", "@mineadmin/search": "^1.0.53", "@mineadmin/table": "^1.0.33", "@vueuse/core": "^12.7.0", "@vueuse/integrations": "^12.7.0", "animate.css": "^4.1.1", "axios": "^1.8.2", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "floating-vue": "5.2.2", "inquirer": "^12.6.3", "lodash-es": "^4.17.21", "md-editor-v3": "^5.3.0", "nprogress": "^0.2.0", "overlayscrollbars": "^2.11.0", "overlayscrollbars-vue": "^0.5.9", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.1", "qs": "^6.14.0", "radash": "^12.1.0", "radix-vue": "^1.9.16", "sortablejs": "1.15.6", "vaul-vue": "^0.3.0", "vue": "^3.5.13", "vue-i18n": "11.1.2", "vue-m-message": "^4.0.2", "vue-router": "^4.5.0", "vue3-sfc-loader": "^0.9.5", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@iconify/json": "^2.2.309", "@iconify/vue": "^4.3.0", "@intlify/unplugin-vue-i18n": "^6.0.3", "@playwright/test": "^1.54.2", "@stylistic/stylelint-config": "^2.0.0", "@types/archiver": "^6.0.3", "@types/mockjs": "^1.0.10", "@types/node": "^24.1.0", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@unocss/eslint-plugin": "^66.0.0", "@vitejs/plugin-legacy": "^6.0.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "ace-builds": "^1.38.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "eslint": "^9.20.1", "esno": "^4.8.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "lint-staged": "^15.4.3", "npm-run-all2": "^7.0.2", "picocolors": "^1.1.1", "postcss": "^8.5.3", "sass": "^1.85.0", "stylelint": "^16.14.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.11.0", "svgo": "^3.3.2", "typescript": "^5.7.3", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vconsole": "^3.15.1", "vite": "^6.2.6", "vite-plugin-chunk-split": "^0.5.0", "vite-plugin-compression2": "^1.3.3", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.2", "vue3-ace-editor": "^2.2.4"}}