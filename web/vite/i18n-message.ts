/**
 * MineAdmin is committed to providing solutions for quickly building web applications
 * Please view the LICENSE file that was distributed with this source code,
 * For the full copyright and license information.
 * Thank you very much for using MineAdmin.
 *
 * <AUTHOR>
 * @Link   https://github.com/mineadmin
 */
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'

export default function createI18nMessage() {
  return VueI18nPlugin({
    include: [
      './src/locales/**',
      './src/modules/**/locales/**',
      './src/plugins/*/**/locales/**',
    ],
  })
}
