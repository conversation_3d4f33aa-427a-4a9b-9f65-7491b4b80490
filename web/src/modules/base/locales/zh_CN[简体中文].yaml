baseUserManage:
  avatar: 头像
  username: 用户名
  nickname: 昵称
  phone: 手机
  email: 邮箱
  password: 密码
  userType: 用户类型
  role: 角色
  signed: 个人签名
  mainTitle: 用户管理
  subTitle: 提供用户添加、编辑、删除功能，超管不可修改。
  setRole: 赋予角色
  setRoleSuccess: 用户角色设置成功
  initPassword: 初始化密码
  setPassword: 是否将用户密码重置为[123456]?
  setPasswordSuccess: 密码重置成功
baseRoleManage:
  mainTitle: 角色管理
  subTitle: 提供用户角色、权限设置
  name: 角色名称
  code: 角色标识
  permission: 权限菜单
  setPermission: 赋予权限
baseMenuManage:
  addError: 此操作已执行过，请点击左侧菜单重新操作。
  deleteChildren: 当前菜单存在子菜单，请先删除子级。
  addTopMenu: 新增顶级菜单
  parentMenu: 上级菜单
  name: 菜单名称
  code: 菜单标识
  url: 菜单路由
  icon: 菜单图标
  type: 菜单类型
  route: 路由地址
  view: 视图地址
  link: 链接/iFrame
  activeName: 高亮菜单
  redirect: 路由重定向
  i18n: 国际化
  sort: 排序
  isEnabled: 是否启用
  isHidden: 是否隐藏
  isCache: 是否缓存
  isCopyright: 版权显示
  isBreadcrumb: 面包屑显示
  isAffix: Tab是否固定
  remark: 备注
  baseInfo: 基础信息
  frontAuth: 前端权限验证
  otherInfo: 其他信息
  typeItem:
    M: 菜单
    B: 按钮
    L: 外链
    I: iFrame
  BtnPermission:
    label: 按钮权限
    name: 按钮名称
    code: 按钮标识
    i18n: 按钮国际化
    noBtn: 没有按钮菜单?
    add: 新增一个
  placeholder:
    name: 请输入菜单名称
    code: 请输入菜单标识，此项全局唯一
    parentMenu: 请选择上级菜单，不选为顶级菜单
    activeName: 请输入菜单标识，访问页面时高亮指定菜单
    route: 页面访问地址，以 "/" 开头
    view: 视图地址，请指向模块或插件下的views目录文件
    link: 请输入第三方URL
    redirect: 利用 vue-router 跳转其他路由地址，以 "/" 开头
    i18n: 菜单标题国际化
    remark: 备注信息
    btnName: 请输入按钮名称
    btnCode: 请输入按钮标识
    btnI18n: 请输入按钮国际化
baseRole:
  name: 角色名称
  code: 角色标识
  remark: 备注
  permission: 权限菜单
  placeholder:
    name: 请输入角色名称
    code: 请输入角色标识，此项全局唯一
    remark: 备注信息
basePermission:
  name: 权限名称
  code: 权限标识
  remark: 备注
  placeholder:
    name: 请输入权限名称
    code: 请输入权限标识，此项全局唯一
    remark: 备注信息
baseDict:
  name: 字典名称
  code: 字典标识
  remark: 备注
  placeholder:
    name: 请输入字典名称
    code: 请输入字典标识，此项全局唯一
    remark: 备注信息
baseDictItem:
  name: 字典项名称
  code: 字典项标识
  remark: 备注
  placeholder:
    name: 请输入字典项名称
    code: 请输入字典项标识，此项全局唯一
baseLoginLog:
  title: 用户登录日志
  username: 用户名
  ip: 登录IP
  browser: 浏览器
  status: 登录状态
  message: 提示消息
  login_time: 登录时间
baseOperationLog:
  title: 用户操作日志
  username: 用户名
  method: 请求方式
  router: 请求路由
  service_name: 业务名称
  ip: 请求IP
  created_at: 创建时间
  updated_at: 更新时间
baseMenu:
  permission:
    index: 权限管理
    user: 用户管理
    userList: 用户列表
    userSave: 用户保存
    userUpdate: 用户更新
    userDelete: 用户删除
    userPassword: 初始用户密码
    getUserRole: 获取用户角色
    setUserRole: 用户赋予角色
    role: 角色管理
    roleList: 角色列表
    roleSave: 角色保存
    roleUpdate: 角色更新
    roleDelete: 角色删除
    getRolePermission: 获取角色权限
    setRolePermission: 赋予角色权限
    menu: 菜单管理
    menuList: 菜单列表
    menuSave: 菜单保存
    menuUpdate: 菜单更新
    menuDelete: 菜单删除
  log:
    index: 日志管理
    userLoginLog: 登录日志
    operationLog: 操作日志
    userLoginLogList: 用户登录日志
    userOperationLog: 用户操作日志
    userLoginLogDelete: 用户登录日志删除
    userOperationLogDelete: 用户操作日志删除
  dataCenter:
    index: 数据中心
    attachment: 附件管理
    attachmentList: 附件列表
    attachmentUpload: 上传附件
    attachmentDelete: 删除附件
