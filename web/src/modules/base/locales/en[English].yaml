baseUserManage:
  avatar: Avatar
  username: Username
  nickname: Nickname
  phone: Phone
  email: Email
  password: Password
  userType: User type
  role: Role
  signed: Signed
  mainTitle: User Manager
  subTitle: Provide users with the functions of adding, editing, and deleting
  setRole: Set role
  setRoleSuccess: The role was set successfully
  initPassword: Init password
  setPassword: Whether to reset the password to 123456?
  setPasswordSuccess: The password was reset successfully
baseRoleManage:
  mainTitle: Role Manager
  subTitle: Provide user roles and permission settings
  name: Role name
  code: Role code
  permission: Permission Menu
  setPermission: Set permission
baseMenuManage:
  addError: This operation has already been performed. Please click the left menu to try again.
  deleteChildren: The current menu has child menus. Please delete the child first.
  addTopMenu: Add Top Menu
  parentMenu: Parent Menu
  name: Menu Name
  code: Menu Code
  url: Menu Route
  icon: Menu Icon
  type: Menu Type
  route: Route
  view: View Address
  link: Link/iFrame
  activeName: Active Name
  redirect: Route Redirect
  i18n: Internationalization
  sort: Sort Order
  isEnabled: Enabled
  isHidden: Hidden
  isCache: Cached
  isCopyright: Copyright
  isBreadcrumb: Breadcrumb
  isAffix: Tab Is Fixed
  remark: Remarks
  typeItem:
    M: Menu
    B: Button
    L: External Link
    I: iFrame
  BtnPermission:
    label: Button Permission
    name: Button Name
    code: Button Code
    i18n: Button Internationalization
    noBtn: No Button Menu?
    add: Add One
  placeholder:
    name: Please enter the menu name
    code: Please enter the menu code, which must be unique globally
    parentMenu: Please select the parent menu, or leave it blank for a top-level menu
    activeName: Please enter the menu code, which is used to highlight the specified menu when the page is accessed
    route: Page access address, starting with "/"
    view: View address, please point to a file in the views directory under the module or plugin
    link: Please enter the third-party URL
    redirect: Use vue-router to redirect to another route address, starting with "/"
    i18n: Menu title internationalization
    remark: Remarks/Notes
    btnName: Please enter the button name
    btnCode: Please enter the button code
    btnI18n: Please enter the button i18n
baseLoginLog:
  title: User Login log
  username: Username
  ip: Login IP
  browser: Browser
  status: Status
  message: Message
  login_time: Login Time
baseOperationLog:
  title: User Operation log
  username: Username
  method: Request Method
  router: Router
  service_name: Service Name
  ip: Request IP
  created_at: Creation Time
  updated_at: Update Time
baseMenu:
  permission:
    index: Permission
    user: User
    userList: User list
    userSave: User save
    userUpdate: User updated
    userDelete: User delete
    userPassword: Init user password
    getUserRole: Get user role
    setUserRole: Set user role
    role: Role
    roleList: Role list
    roleSave: Role save
    roleUpdate: Role updated
    roleDelete: Role delete
    getRolePermission: Get role permission
    setRolePermission: Set role permission
    menu: Menu
    menuList: Menu list
    menuSave: Menu save
    menuUpdate: Menu updated
    menuDelete: Menu delete
  log:
    index: Log Management
    userLoginLog: Login Log
    operationLog: Operation Log
    userLoginLogList: User Login Log List
    userOperationLog: User Operation Log List
    userLoginLogDelete: User Login Log Delete
    userOperationLogDelete: User Operation Log Delete
  dataCenter:
    index: Data Center
    attachment: Attachment
    attachmentList: Attachment List
    attachmentUpload: Attachment Upload
    attachmentDelete: Attachment Delete
