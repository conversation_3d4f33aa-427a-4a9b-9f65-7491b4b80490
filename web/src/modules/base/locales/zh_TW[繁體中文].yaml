baseUserManage:
  avatar: 頭像
  username: 用戶名
  nickname: 昵稱
  phone: 手機
  email: 郵箱
  password: 密碼
  userType: 用戶類型
  signed: 個人簽名
  role: 角色
  mainTitle: 用戶管理
  subTitle: 提供使用者添加、編輯、刪除功能，超管不可修改。
  setRole: 賦予角色
  setRoleSuccess: 用戶角色設置成功
  initPassword: 重設密碼
  setPassword: 是否將密碼重設為[123456]?
  setPasswordSuccess: 密碼重設完畢
baseRoleManage:
  mainTitle: 角色管理
  subTitle: 提供用戶角色、權限設定
  name: 角色名稱
  code: 角色標識
  permission: 權限菜單
  setPermission: 賦予權限
baseMenuManage:
  addError: 此操作已執行過，請點擊左側選單重新操作。
  deleteChildren: 該菜單存在子菜單，請先刪除子級。
  addTopMenu: 新增頂級菜單
  parentMenu: 上級菜單
  name: 菜單名稱
  code: 菜單標識
  url: 菜單路由
  icon: 菜單圖標
  type: 菜單類型
  route: 路由地址
  view: 視圖地址
  link: 鏈接/iFrame
  activeName: 高亮菜單
  redirect: 路由重定向
  i18n: 國際化
  sort: 排序
  isEnabled: 是否啟用
  isHidden: 是否隱藏
  isCache: 是否緩存
  isCopyright: 版權顯示
  isBreadcrumb: 麵包屑顯示
  isAffix: Tab是否固定
  remark: 備註
  typeItem:
    M: 菜單
    B: 按鍵
    L: 外鏈
    I: iFrame
  BtnPermission:
    label: 按鍵權限
    name: 按鍵名稱
    code: 按鍵標識
    i18n: 按鍵國際化
    noBtn: 沒有按鍵菜單?
    add: 新增一個
  placeholder:
    name: 請輸入菜單名稱
    code: 請輸入菜單標識，此項全局唯一
    parentMenu: 請選擇上級菜單，不選為頂級菜單
    activeName: 請輸入菜單標識，訪問頁面時高亮指定菜單
    route: 頁面訪問地址，以 "/" 開頭
    view: 視圖地址，請指向模組或插件下的views目錄文件
    link: 請輸入第三方網址
    redirect: 利用 vue-router 跳轉其他路由地址，以 "/" 開頭
    i18n: 菜單標題國際化
    remark: 備註信息
    btnName: 請輸入按鍵名稱
    btnCode: 請輸入按鍵標識
    btnI18n: 請輸入按鍵國際化
baseLoginLog:
  title: 用戶登錄日志
  username: 使用者名稱
  ip: 登入IP
  browser: 瀏覽器
  status: 登入狀態
  message: 提示訊息
  login_time: 登入時間
baseOperationLog:
  title: 用戶操作日誌
  username: 使用者名稱
  method: 請求方式
  router: 請求路由
  service_name: 業務名稱
  ip: 請求IP
  created_at: 建立時間
  updated_at: 更新時間
baseMenu:
  permission:
    index: 權限管理
    user: 用戶管理
    userList: 用戶列表
    userSave: 用戶保存
    userUpdate: 用戶更新
    userDelete: 用戶刪除
    userPassword: 重設用戶密码
    getUserRole: 查詢用戶角色
    setUserRole: 設置用戶角色
    role: 角色管理
    roleList: 角色列表
    roleSave: 角色保存
    roleUpdate: 角色更新
    roleDelete: 角色刪除
    getRolePermission: 查詢角色權限
    setRolePermission: 賦予角色權限
    menu: 菜單管理
    menuList: 菜單管列表
    menuSave: 菜單管保存
    menuUpdate: 菜單管更新
    menuDelete: 菜單管刪除
  log:
    index: 日志管理
    userLoginLog: 登錄日志
    operationLog: 操作日志
    userLoginLogList: 用戶登錄日志
    userOperationLog: 用戶操作日志
    userLoginLogDelete: 用戶登錄日志刪除
    userOperationLogDelete: 用戶操作日志刪除
  dataCenter:
    index: 數據中心
    attachment: 附件管理
    attachmentList: 附件列表
    attachmentUpload: 上傳附件
    attachmentDelete: 刪除附件
