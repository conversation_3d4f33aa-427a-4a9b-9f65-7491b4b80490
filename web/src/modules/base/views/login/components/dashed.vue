<!--
 - Mine<PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://github.com/mineadmin
-->
<template>
  <svg
    viewBox="0 0 881 211" fill="white" aria-hidden="true"
    class="absolute right-0 top-[50%] z-5 w-[55.0625rem] origin-top-right rotate-[30deg] animate-pulse overflow-visible opacity-70"
  >
    <defs>
      <filter id=":R1cpuja:">
        <feGaussianBlur in="SourceGraphic" stdDeviation=".5" />
      </filter>
    </defs>
    <path
      stroke="white" stroke-opacity="0.2" stroke-dasharray="1" stroke-dashoffset="1" pathLength="1"
      fill="transparent" d="M 247,103L261,86L307,104L357,36" class="invisible"
      style=" visibility: visible;stroke-dashoffset: 0;"
    />
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="247" cy="103" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 15.4375rem 6.4375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="261" cy="86" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 16.3125rem 5.375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="307" cy="104" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 19.1875rem 6.5rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="357" cy="36" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 22.3125rem 2.25rem;"
      />
    </g>
    <path
      stroke="white" stroke-opacity="0.2" stroke-dasharray="1" stroke-dashoffset="1" pathLength="1"
      fill="transparent" d="M 586,120L516,100L491,62L440,107L477,180L516,100" class="invisible"
      style=" visibility: visible; fill: rgb(255 255 255 / 2%);stroke-dashoffset: 0;"
    />
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="586" cy="120" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 36.625rem 7.5rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="516" cy="100" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 32.25rem 6.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="491" cy="62" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 30.6875rem 3.875rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="440" cy="107" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 27.5rem 6.6875rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="477" cy="180" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 29.8125rem 11.25rem;"
      />
    </g>
    <path
      stroke="white" stroke-opacity="0.2" stroke-dasharray="1" stroke-dashoffset="1" pathLength="1"
      fill="transparent" d="M 733,100L803,120L879,113L823,164L803,120" class="invisible"
      style=" visibility: visible; fill: rgb(255 255 255 / 2%);stroke-dashoffset: 0;"
    />
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="733" cy="100" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 45.8125rem 6.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="803" cy="120" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 50.1875rem 7.5rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="879" cy="113" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 54.9375rem 7.0625rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="823" cy="164" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 51.4375rem 10.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="4" cy="4" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 0.25rem 0.25rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="4" cy="44" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 0.25rem 2.75rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="36" cy="22" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 2.25rem 1.375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="50" cy="146" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 3.125rem 9.125rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="64" cy="43" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 4rem 2.6875rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="76" cy="30" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 4.75rem 1.875rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="101" cy="116" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 6.3125rem 7.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="140" cy="36" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 8.75rem 2.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="149" cy="134" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 9.3125rem 8.375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="162" cy="74" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 10.125rem 4.625rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="171" cy="96" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 10.6875rem 6rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="210" cy="56" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 13.125rem 3.5rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="235" cy="90" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 14.6875rem 5.625rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="275" cy="82" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 17.1875rem 5.125rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="306" cy="6" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 19.125rem 0.375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="307" cy="64" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 19.1875rem 4rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="380" cy="68" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 23.75rem 4.25rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="380" cy="108" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 23.75rem 6.75rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="391" cy="148" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 24.4375rem 9.25rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="405" cy="18" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 25.3125rem 1.125rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="412" cy="86" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 25.75rem 5.375rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="426" cy="210" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 26.625rem 13.125rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="427" cy="56" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 26.6875rem 3.5rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="538" cy="138" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 33.625rem 8.625rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="563" cy="88" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 35.1875rem 5.5rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="611" cy="154" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 38.1875rem 9.625rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="637" cy="150" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 39.8125rem 9.375rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="651" cy="146" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 40.6875rem 9.125rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="682" cy="70" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 42.625rem 4.375rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="683" cy="128" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 42.6875rem 8rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="781" cy="82" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 48.8125rem 5.125rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="785" cy="158" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 49.0625rem 9.875rem;"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="832" cy="146" r="1"
        style=" opacity: 0.2; transform: scale(var(--motion-scale));transform-origin: 52rem 9.125rem;"
        filter="url(#:R1cpuja:)"
      />
    </g>
    <g class="opacity-0" style="opacity: 1;">
      <circle
        cx="852" cy="89" r="1"
        style=" opacity: 1; transform: scale(var(--motion-scale));transform-origin: 53.25rem 5.5625rem;"
      />
    </g>
  </svg>
</template>
