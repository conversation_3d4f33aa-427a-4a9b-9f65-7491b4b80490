// 侧边栏子菜单动画
.mine-sub-aside-container,
.mine-sub-aside {
  @apply
    relative
    flex flex-col
    overflow-x-hidden h-full
    bg-[#f9fafc]
    dark-bg-dark-8
    duration-300 text-sm
    border-r-1 border-r-solid
    border-r-[#e8e9ec]
    dark-border-r-dark-5
  ;

  transition: width 0.3s, transform 0.3s, box-shadow 0.3s, top 0.3s;

  & .mine-sub-aside-list {
    @apply h-full overflow-y-auto pb-2;

    overscroll-behavior: contain;

    // firefox隐藏滚动条
    scrollbar-width: none;

    // chrome隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    &.shadow-top {
      box-shadow: inset 0 10px 10px -10px var(--mine-g-box-shadow-color), inset 0 0 0 transparent;
    }

    &.shadow-bottom {
      box-shadow: inset 0 0 0 transparent, inset 0 -10px 10px -10px var(--mine-g-box-shadow-color);
    }

    &.shadow-top.shadow-bottom {
      box-shadow: inset 0 10px 10px -10px var(--mine-g-box-shadow-color), inset 0 -10px 10px -10px var(--mine-g-box-shadow-color);
    }
  }

  & .mine-sub-aside-close-button,
  & .mine-sub-aside-collapse-button,
  & .mine-sub-aside-fixed-button {
    @apply flex-center cursor-pointer rounded bg-stone-1 p-2 transition
      bg-gray-2 hover-bg-gray-3 text-dark-5
      dark-bg-dark-3 hover-dark-bg-dark-1 dark-text-gray-1
    ;
  }
}

.mine-sub-aside-container-enter-active,
.mine-sub-aside-container-leave-active {
  transition: transform 0.35s;
}

.mine-sub-aside-container-enter-from,
.mine-sub-aside-container-leave-to {
  transform: translateX(calc(var(--mine-g-sub-aside-width) * -1));
}

.mine-sub-aside-container-leave-active {
  position: absolute;
}
