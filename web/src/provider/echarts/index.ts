/**
 * MineAdmin is committed to providing solutions for quickly building web applications
 * Please view the LICENSE file that was distributed with this source code,
 * For the full copyright and license information.
 * Thank you very much for using MineAdmin.
 *
 * <AUTHOR>
 * @Link   https://github.com/mineadmin
 */
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pie<PERSON>hart, RadarChart } from 'echarts/charts'
import { CanvasRenderer, SVGRenderer } from 'echarts/renderers'
import {
  DataZoomComponent,
  GraphicComponent,
  GridComponent,
  LegendComponent,
  PolarComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from 'echarts/components'
import type { App } from 'vue'
import mineDarkJson from './themes/mineDark.project.json'
import type { ProviderService } from '#/global'

const { use } = echarts

use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>rid<PERSON>omponent,
  <PERSON><PERSON>omponent,
  Polar<PERSON>omponent,
  LegendComponent,
  GraphicComponent,
  <PERSON>lboxComponent,
  <PERSON>ltip<PERSON>omponent,
  DataZoomComponent,
  VisualMapComponent,
])

const provider: ProviderService.Provider = {
  name: 'echarts',
  setProvider(app: App): void {
    echarts.registerTheme('mineDark', mineDarkJson.theme as any)
    app.config.globalProperties.$echarts = echarts
  },
  getProvider() {
    return useGlobal().$echarts
  },
}

export default provider as ProviderService.Provider
